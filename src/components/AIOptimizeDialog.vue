<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="!isOptimizing"
    width="80%"
    class="ai-optimize-dialog"
    append-to-body
    @close="handleClose"
  >
    <div class="ai-optimize-content">
      <!-- 当前内容 -->
      <div class="current-content">
        <div class="content-header">
          <h4>当前{{ contentLabel }}</h4>
        </div>
                 <div class="content-body">
           <el-input
             type="textarea"
             v-model="editableCurrentContent"
             :rows="contentRows"
             :readonly="isOptimizing"
             :class="{ 'readonly-textarea': isOptimizing, 'markdown-textarea': true }"
           />
         </div>
      </div>

      <!-- AI优化建议 -->
      <div class="optimized-content">
                 <div class="content-header">
           <h4>AI优化建议</h4>
           <div class="header-buttons">
             <el-button
               v-if="optimizedContent && !isOptimizing"
               type="success"
               size="mini"
               @click="handleAdopt"
             >
               <i class="el-icon-check"></i>
               采纳
             </el-button>
             <el-button
               type="primary"
               size="mini"
               :loading="isOptimizing"
               :disabled="isOptimizing"
               @click="handleOptimize"
             >
               <img class="ai-optimize-icon" src="@/assets/app/<EMAIL>" alt="AI优化" />
               {{ optimizeButtonText }}
             </el-button>
           </div>
         </div>
        <div class="content-body">
          <div v-if="!optimizedContent && !isOptimizing" class="empty-state">
            <img class="ai-optimize-icon-large" src="@/assets/app/<EMAIL>" alt="AI优化" />
            <p>点击AI优化按钮生成优化建议</p>
          </div>
          <div v-else-if="isOptimizing" class="loading-state" v-loading="true" element-loading-text="正在帮您生成，请稍后......">
            <div style="height: 200px;"></div>
          </div>
          <el-input
            v-else
            type="textarea"
            v-model="optimizedContent"
            :rows="contentRows"
            maxlength="2000"
            show-word-limit
            placeholder="AI优化建议将在这里显示"
            class="markdown-textarea"
          />
        </div>
      </div>
    </div>

         <span slot="footer" class="dialog-footer">
       <el-button
         :disabled="isOptimizing"
         @click="handleClose"
       >
         取消
       </el-button>
       <el-button
         type="primary"
         :disabled="isOptimizing"
         @click="handleConfirm"
       >
         确认
       </el-button>
     </span>
  </el-dialog>
</template>

<script>
import { api } from "@/api/request";

export default {
  name: "AIOptimizeDialog",
  // 注意：此组件保持AI优化后的原始markdown格式，不使用formatOutputContent转换
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: "AI优化"
    },
    contentLabel: {
      type: String,
      default: "内容"
    },
    currentContent: {
      type: String,
      default: ""
    },
    contentRows: {
      type: Number,
      default: 12
    },
    optimizeType: {
      type: String,
      default: "description" // description, introduction, settings
    },
    additionalParams: {
      type: Object,
      default: () => ({})
    }
  },
     data() {
     return {
       dialogVisible: false,
       isOptimizing: false,
       optimizedContent: "",
       hasOptimized: false, // 标记是否已经优化过
       hasAdopted: false, // 标记是否已经采纳
       editableCurrentContent: "" // 可编辑的当前内容
     };
   },
  computed: {
    optimizeButtonText() {

      if (this.isOptimizing) {
        return "优化中...";
      }
      return this.hasOptimized ? "AI重新优化" : "AI优化";
    }
  },
     watch: {
     visible: {
       handler(newVal) {
         this.dialogVisible = newVal;
         if (newVal) {
           // 每次打开弹窗时重置状态
           this.optimizedContent = "";
           this.hasOptimized = false;
           this.isOptimizing = false;
           this.hasAdopted = false;
           this.editableCurrentContent = this.currentContent;
         }
       },
       immediate: true
     },
     currentContent: {
       handler(newVal) {
         this.editableCurrentContent = newVal;
       },
       immediate: true
     }
   },
  methods: {
    async handleOptimize() {
      if (this.isOptimizing) return;

      // 优化前进行校验
      if (!this.validateBeforeOptimize()) {
        return;
      }

      try {
        this.isOptimizing = true;

        // 调用AI优化接口
        const optimizedText = await this.callOptimizeAPI();

        this.optimizedContent = optimizedText;
        this.hasOptimized = true;

        this.$message.success("AI优化完成");
      } catch (error) {
        console.error("AI优化失败:", error);
        this.$showFriendlyError(error, "AI优化失败，请稍后重试");
      } finally {
        this.isOptimizing = false;
      }
    },

    validateBeforeOptimize() {
      // 统一校验：只需要智能体名称和智能体分类
      if (!this.additionalParams.name || !this.additionalParams.name.trim()) {
        this.$showFriendlyError(null, '请先填写智能体名称');
        return false;
      }

      if (!this.additionalParams.applicationType || !this.additionalParams.applicationType.trim()) {
        this.$showFriendlyError(null, '请先选择智能体分类');
        return false;
      }

      return true;
    },

    async callOptimizeAPI() {
      // 构建请求参数
      const baseParams = {
        agentName: this.additionalParams.name || "",
        agentCategory: this.additionalParams.applicationType || "其他",
        model: "qwen-turbo" // 可以从配置中获取
      };

      let apiMethod = null;
      let requestParams = {};

      // 根据优化类型选择对应的接口和构建参数
      switch (this.optimizeType) {
        case "description":
          apiMethod = api.agentService.optimizeAgentDescription;
          requestParams = {
            ...baseParams,
            currentDescription: this.editableCurrentContent || "",
            require: `你是一个专业的AI智能体描述优化师。

## 任务
基于智能体名称"${this.additionalParams.name || '[智能体名称]'}"、应用分类"${this.additionalParams.applicationType || '[应用分类]'}"和当前描述，生成一个专业、精炼的智能体描述。

## 输出格式
必须严格按照以下格式输出：

\`\`\`markdown
## 智能体描述

一个专注于[具体领域]的[核心功能]工具，旨在帮助[目标用户][实现的价值]。
\`\`\`

## 要求
- 描述必须是一句话，包含功能、用户、价值三要素
- 使用第三人称视角（"一个..."、"一款..."）
- 语言专业、简洁、有力
- 严格按照markdown代码块格式输出

## 示例
智能体名称：文案大师，应用分类：内容创作
输出：一个专注于营销文案创作的AI工具，旨在帮助营销人员快速生成高转化率的广告内容。

请为"${this.additionalParams.name || '[智能体名称]'}"（${this.additionalParams.applicationType || '[应用分类]'}）生成描述：`,
            text: this.editableCurrentContent || ""
          };
          break;
        case "introduction":
          apiMethod = api.agentService.optimizeAgentDescription;
          requestParams = {
            ...baseParams,
            currentDescription: this.additionalParams.description || "",
            require: `你是一个专业的AI智能体开场白优化师。

## 任务
基于智能体名称"${this.additionalParams.name || '[智能体名称]'}"、应用分类"${this.additionalParams.applicationType || '[应用分类]'}"，生成一个专业、引导性强的开场白。

## 输出格式
必须严格按照以下格式输出：

\`\`\`markdown
## 开场介绍

您好！我是[角色身份]，专注于[核心价值/服务领域]。我可以帮助您[具体能做什么]。

请告诉我您的需求，或者直接输入相关问题，我将为您提供专业的帮助。
\`\`\`

## 要求
- 必须包含问候语（您好！）
- 明确身份介绍（我是...）
- 说明核心价值（专注于...）
- 说明具体能力（可以帮助您...）
- 引导用户行动（请告诉我...）
- 语气要与应用分类匹配
- 严格按照markdown代码块格式输出

## 示例
智能体名称：智能客服，应用分类：客服
输出：您好！我是智能客服助手，专注于为用户提供高效的问题解答服务。我可以帮助您处理订单查询、售后服务、产品咨询等各类问题。

请告诉我您的需求，或者直接输入相关问题，我将为您提供专业的帮助。

请为"${this.additionalParams.name || '[智能体名称]'}"（${this.additionalParams.applicationType || '[应用分类]'}）生成开场白：`,
            text: this.editableCurrentContent || ""
          };
          break;
        case "settings":
          apiMethod = api.agentService.optimizeAgentDescription;
          requestParams = {
            ...baseParams,
            currentDescription: this.additionalParams.description || "",
            require: `你是一位顶级的提示词（Prompt）架构师。请严格按照以下要求执行任务：

**任务目标：**
基于智能体名称"${this.additionalParams.name || '[智能体名称]'}"、智能体描述"${this.additionalParams.description || '[智能体描述]'}"、应用分类"${this.additionalParams.applicationType || '[应用分类]'}"，生成一个结构化的六段式提示词。

**输出格式要求（必须严格遵守）：**
你必须完全按照以下格式输出，不允许有任何偏差：

\`\`\`markdown
## 角色
[角色定义，一句话说明身份和专业定位]

## 目标
[明确要完成的核心任务或解决的关键问题]

## 技能
- [技能1：具体能力描述]
- [技能2：具体能力描述]
- [技能3：具体能力描述]

## 工作流
1. [第一步：具体操作]
2. [第二步：具体操作]
3. [第三步：具体操作]
4. [第四步：输出结果]

## 注意
- [注意事项1]
- [注意事项2]
- [输出格式要求]

## 示例
**用户输入：**[示例输入]
**输出：**[示例输出]
\`\`\`

**严格要求：**
1. 你的回答必须且只能是上述markdown代码块格式
2. 绝对不允许在代码块前后添加任何解释、说明或其他文字
3. 必须包含所有六个部分：角色、目标、技能、工作流、注意、示例
4. 每个部分都要有实际内容，不能为空
5. 开始你的回答时，第一个字符必须是反引号 \`

现在请为智能体"${this.additionalParams.name || '[智能体名称]'}"生成提示词：`,
            text: this.editableCurrentContent || ""
          };
          break;
        default:
          throw new Error("不支持的优化类型");
      }

      // 调用真实API
      const res = await apiMethod(requestParams);
      if (res.isSuccess && res.data && res.data.optimizedDescription) {
        // 添加调试日志
        console.log('=== AI优化原始返回内容 ===');
        console.log('返回结果:', res.data.optimizedDescription);
        console.log('包含转义换行符:', res.data.optimizedDescription.includes('\\n'));
        console.log('========================');

        // 提取markdown代码块中的内容，保持原始markdown格式
        const extractedContent = this.extractMarkdownContent(res.data.optimizedDescription);
        console.log('=== 提取后的内容（保持完整markdown格式）===');
        console.log('提取结果:', extractedContent);
        console.log('包含真实换行符:', extractedContent.includes('\n'));
        console.log('包含markdown代码块:', extractedContent.includes('```'));
        console.log('内容长度:', extractedContent.length);
        console.log('完整内容预览:\n', extractedContent);
        console.log('====================================');

        return extractedContent;
      } else {
        throw new Error(res.message || 'AI优化失败');
      }
    },

    // 根据优化类型处理AI返回的内容
    // settings类型：保持完整的markdown格式（包含代码块标识符）
    // description/introduction类型：提取纯文本内容，去除markdown格式
    extractMarkdownContent(text) {
      if (!text) return '';

      // 首先处理转义的换行符，将 \n 转换为真实的换行符
      // 同时处理可能的双重转义情况
      let processedText = text
        .replace(/\\\\n/g, '\n')  // 处理双重转义的换行符 \\n -> \n
        .replace(/\\n/g, '\n')    // 处理单重转义的换行符 \n -> 真实换行符
        .replace(/\\t/g, '\t')    // 处理制表符
        .replace(/\\r/g, '\r');   // 处理回车符

      // 对于settings类型，保留完整的markdown格式
      if (this.optimizeType === 'settings') {
        // 检查是否包含markdown代码块，如果包含则保留完整结构
        if (processedText.includes('```markdown') || processedText.includes('```')) {
          // 直接返回处理后的完整文本，保留markdown代码块标识符
          return processedText.trim()
            .replace(/^(这是优化后的|优化后的|以下是优化后的|根据您的要求|按照要求)[^：:]*[：:]\s*/i, '') // 移除解释性开头
            .replace(/^(智能体描述|开场白|提示词)[：:]\s*/i, '') // 移除标签开头
            .trim();
        }
        // 对于settings类型，如果没有代码块也直接返回
        return processedText.trim()
          .replace(/^(这是优化后的|优化后的|以下是优化后的|根据您的要求|按照要求)[^：:]*[：:]\s*/i, '')
          .replace(/^(智能体描述|开场白|提示词)[：:]\s*/i, '')
          .trim();
      }

      // 对于description和introduction类型，提取markdown代码块内的纯文本内容
      if (processedText.includes('```markdown') || processedText.includes('```')) {
        // 提取markdown代码块之间的内容
        const markdownMatch = processedText.match(/```(?:markdown)?\s*\n?([\s\S]*?)\n?```/);
        if (markdownMatch && markdownMatch[1]) {
          let content = markdownMatch[1].trim();

          // 移除markdown标题符号和特定标题文字
          content = content.replace(/^#+\s+/gm, ''); // 移除 # ## ### 等标题符号
          content = content.replace(/^(智能体描述|开场介绍|开场白|提示词)\s*$/gm, ''); // 移除特定标题文字行

          // 移除markdown格式标记，但保留文本内容
          content = content
            .replace(/\*\*(.*?)\*\*/g, '$1') // 移除粗体标记
            .replace(/\*(.*?)\*/g, '$1')      // 移除斜体标记
            .replace(/`(.*?)`/g, '$1')       // 移除行内代码标记
            .replace(/\[(.*?)\]\(.*?\)/g, '$1') // 移除链接，保留文本
            .replace(/\n\s*\n/g, '\n') // 移除多余的空行
            .trim();

          return content;
        }
      }

      // 如果没有代码块，按原来的逻辑处理
      // 检查是否以 ## 开头（可能大模型直接输出了标题内容）
      if (processedText.startsWith('## ')) {
        // 移除第一行标题，返回后续内容
        const lines = processedText.split('\n');
        if (lines.length > 1) {
          return lines.slice(1).join('\n').trim();
        }
      }

             // 最后的备选方案：返回原文本，但进行一些清理
       // 移除markdown格式标记
       let cleanedText = processedText.trim()
         .replace(/^(这是优化后的|优化后的|以下是优化后的|根据您的要求|按照要求)[^：:]*[：:]\s*/i, '') // 移除解释性开头
         .replace(/^(智能体描述|开场白|开场介绍|提示词)[：:]\s*/i, '') // 移除标签开头
         .replace(/^#+\s+/gm, '') // 移除markdown标题符号
         .replace(/^(智能体描述|开场介绍|开场白|提示词)\s*$/gm, '') // 移除特定标题文字行
         .replace(/\*\*(.*?)\*\*/g, '$1') // 移除粗体标记
         .replace(/\*(.*?)\*/g, '$1')      // 移除斜体标记
         .replace(/`(.*?)`/g, '$1')       // 移除行内代码标记
         .replace(/\[(.*?)\]\(.*?\)/g, '$1') // 移除链接，保留文本
         .replace(/\n\s*\n/g, '\n') // 移除多余的空行
         .trim();

      return cleanedText;
    },

    handleAdopt() {
      // 采纳AI优化建议：将右侧内容复制到左侧
      this.editableCurrentContent = this.optimizedContent;
      this.hasAdopted = true;
      this.$message.success("已采纳AI优化建议，您可以继续编辑");
    },

    handleConfirm() {
      if (this.isOptimizing) return;

      // 点击确定时进行必填校验
      if (!this.validateBeforeOptimize()) {
        return;
      }

      // 触发确认事件，将当前编辑的内容传递给父组件
      this.$emit("confirm", this.editableCurrentContent);
      this.handleClose();
    },

    handleClose() {
      if (this.isOptimizing) return;

      this.$emit("update:visible", false);
      this.$emit("close");
    },

    // 调试方法：用于测试markdown格式转换
    testMarkdownConversion() {
      const testInput1 = "```markdown\n一个专注于客户服务响应的AI工具，旨在帮助企业在高频咨询场景中提升服务效率与客户满意度。\n```";
      const testInput2 = "```markdown\\n## \\n\\n您好！我是测试33333，专注于客户服务流程优化与多渠道响应效率提升。我可以帮助您实现自动化应答、问题分类处理、用户满意度提升等核心功能。\\n\\n请告诉我您的需求，或者直接输入相关问题，我将为您提供专业的帮助。\\n```";

      console.log('=== 测试1：简单markdown代码块 ===');
      const result1 = this.extractMarkdownContent(testInput1);
      console.log('输入:', testInput1);
      console.log('输出:', result1);

      console.log('=== 测试2：带转义字符的markdown代码块 ===');
      const result2 = this.extractMarkdownContent(testInput2);
      console.log('输入:', testInput2);
      console.log('输出:', result2);

      return { result1, result2 };
    }
  }
};
</script>

<style lang="scss" scoped>
.ai-optimize-dialog {
  .ai-optimize-content {
    display: flex;
    gap: 20px;
    height: 500px;

    .current-content,
    .optimized-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      border: 1px solid #ebeef5;
      border-radius: 8px;
      overflow: hidden;
    }

    .content-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 20px;
      background-color: #f8f9fa;
      border-bottom: 1px solid #ebeef5;

      h4 {
        margin: 0;
        font-size: 14px;
        font-weight: 500;
        color: #303133;
      }

      .header-buttons {
        display: flex;
        gap: 8px;
        align-items: center;
      }
    }

    .content-body {
      flex: 1;
      padding: 20px;
      display: flex;
      flex-direction: column;
      position: relative;

      .readonly-textarea {
        height: 100%;

        :deep(.el-textarea__inner) {
          height: 100% !important;
          resize: none;
          background-color: #f8f9fa;
          border: none;
          color: #606266;
        }
      }

      .el-input {
        height: 100%;

        :deep(.el-textarea__inner) {
          height: 100% !important;
          resize: none;
        }
      }

            // markdown格式文本框的特殊样式
      .markdown-textarea {
        :deep(.el-textarea__inner) {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
          font-size: 13px;
          line-height: 1.6;
          color: #2c3e50;
          background-color: #fafafa;
          border: 1px solid #e1e8ed;
          white-space: pre-wrap; // 保持空格和换行符
          word-wrap: break-word; // 长单词换行

          // 为markdown语法添加更好的可读性
          &::placeholder {
            color: #909399;
            font-family: inherit;
          }
        }
      }

      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #909399;

        i {
          font-size: 48px;
          margin-bottom: 16px;
          color: #d1d5db;
        }

        p {
          margin: 0;
          font-size: 14px;
        }
      }

      .loading-state {
        height: 100%;
        position: relative;
      }
    }
  }

  .dialog-footer {
    .el-button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

// AI优化图标样式
.ai-optimize-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
  vertical-align: middle;
}

.ai-optimize-icon-large {
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

// 自定义loading样式
:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.8);
}

:deep(.el-loading-text) {
  color: #409eff;
  font-size: 14px;
}
</style>
