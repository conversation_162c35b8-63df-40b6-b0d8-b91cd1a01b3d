<template>
  <div class="create-app-container">
    <div class="create-app-header">
      <h2>创建智能体</h2>
    </div>

            <!-- 智能体类型选择 -->
    <div class="app-type-selection">
      <el-row :gutter="20">
        <el-col :span="12">
          <div
            class="app-type-card"
            :class="{ active: appType === 'light' }"
            @click="selectAppType('light')"
          >
            <div class="app-type-icon">
              <i class="el-icon-lightning"></i>
            </div>
            <div class="app-type-content">
              <h3>轻量智能体</h3>
              <p>适用于简单对话和基础任务处理</p>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div
            class="app-type-card"
            :class="{ active: appType === 'knowledge' }"
            @click="selectAppType('knowledge')"
          >
            <div class="app-type-icon">
              <i class="el-icon-collection"></i>
            </div>
            <div class="app-type-content">
              <h3>知识智能体</h3>
              <p>适用于查询知识库、数据库能力</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <div class="quick-actions">
      <el-button type="text" @click="handleAIFill">
        <i class="el-icon-magic-stick"></i>
        AI一键填写
          <el-tooltip content="填写智能体名称和描述后，由AI帮你一键生成其他内容。注意对于'智能体设定'这样的关键配置，建议在生成后进行优化，以获得更好的效果。" placement="top">
          <i class="el-icon-question"></i>
        </el-tooltip>
      </el-button>
      <el-button type="text" @click="goToTemplates"> 去模板中心复制 </el-button>
    </div>

    <!-- 应用信息表单 -->
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-position="left"
      label-width="100px"
      class="app-form"
    >
        <el-form-item label="智能体名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入智能体名称"
        ></el-input>
      </el-form-item>

        <el-form-item label="智能体描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="2"
          placeholder="智能体是做什么的？简单描述它吧"
          :maxlength="100"
          show-word-limit
        ></el-input>
      </el-form-item>

      <el-form-item label="开场介绍" prop="introduce">
        <el-tooltip
          content="这是用户在网页端首次使用时，智能应用向用户发送的第一条消息，支持Markdown格式。且支持使用 '[]' 符号生成可以点击发送的引导问题，例如:我是你的智能助手，你可以问我:- [如何进行活动策划?]"
          placement="top"
        >
          <i class="el-icon-question"></i>
        </el-tooltip>
        <el-input
          v-model="formData.introduce"
          type="textarea"
          :rows="4"
          placeholder="智能体发送的首句消息，例如：“需要什么帮助吗？”"
          :maxlength="500"
          show-word-limit
        ></el-input>
      </el-form-item>

      <el-form-item label="智能体头像" prop="profilePhoto">
        <el-upload
          class="avatar-uploader"
          action="#"
          :show-file-list="false"
          :before-upload="beforeAvatarUpload"
          :http-request="handleAvatarUpload"
        >
          <img
            v-if="formData.profilePhoto"
            :src="formData.profilePhoto"
            class="avatar"
          />
          <div v-else class="upload-area">
            <i class="el-icon-plus"></i>
          </div>
        </el-upload>
        <div class="form-tip">支持 jpg、png 格式，大小不超过 2MB</div>
      </el-form-item>

      <el-form-item label="是否公开" prop="isPublic">
        <div class="switch-container">
          <div class="switch-group">
            <span class="switch-label">否</span>
            <el-switch v-model="formData.isPublic"></el-switch>
            <span class="switch-label">是</span>
          </div>
          <div class="form-tip">如选择公开则可在应用广场被搜索到使用</div>
        </div>
      </el-form-item>

      <el-form-item label="智能体分类" prop="applicationTypeCode">
        <el-select
          v-model="formData.applicationTypeCode"
          placeholder="请选择智能体分类"
          @change="handleCategoryChange"
        >
          <el-option
            v-for="item in categories"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="智能体设定" prop="SessionFlowSetting">
        <div class="setting-container">
          <div class="form-item-header">
            <el-tooltip
              content=" 智能体设定非常重要，决定了机器人的人物设定、功能和工作特征，请尽可能用详细的自然语言进行描述，越具体效果越好哦"
              placement="top"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </div>

          <el-input
            v-model="formData.SessionFlowSetting"
            type="textarea"
            :rows="4"
            placeholder="输入提示词，定义智能体行为"
          ></el-input>
        </div>
      </el-form-item>

      <!-- 知识库选择，仅在知识智能体时显示 -->
      <template v-if="appType === 'knowledge'">
        <el-form-item label="知识库绑定" v-if="!showUploadSection">
          <div class="knowledge-base-selection">
            <div class="selected-knowledge">
              <span v-if="selectedKnowledgeIds.length === 0">请选择知识库</span>
              <div v-else class="knowledge-tags">
                <el-tag
                  v-for="kb in selectedKnowledgeList"
                  :key="kb.id"
                  closable
                  @close="removeKnowledgeSelection(kb.id)"
                  class="knowledge-tag"
                >
                  {{ kb.name }}
                </el-tag>
              </div>
            </div>
            <el-button type="primary" @click="showKnowledgeDialog"
              >选择知识库</el-button
            >
          </div>
          <div class="form-tip">选择需要绑定的知识库，支持多个知识库</div>
          <div class="direct-upload">
            <el-button
              type="text"
              class="direct-upload-btn"
              @click="toggleUploadSection"
            >
              <i class="el-icon-upload2"></i>
              直接上传文件
            </el-button>
          </div>
        </el-form-item>

        <el-form-item
          v-if="showUploadSection"
          label="上传文档"
          prop="documents"
        >
          <div class="document-upload">
            <div class="document-types">
              <div class="doc-type-label">上传文档</div>
              <div class="doc-type-options">
                <div
                  class="doc-type-item"
                  :class="{ active: currentFileType === 'noStructure' }"
                  @click="selectFileType('noStructure')"
                >
                  <div class="doc-type-icon">
                    <i class="el-icon-document"></i>
                  </div>
                  <div class="doc-type-content">
                    <div class="doc-type-title">无结构文档</div>
                    <div class="doc-type-desc">自动解析文档，使用方便</div>
                  </div>
                </div>
                <div
                  class="doc-type-item"
                  :class="{ active: currentFileType === 'qa' }"
                  @click="selectFileType('qa')"
                >
                  <div class="doc-type-icon">
                    <i class="el-icon-question"></i>
                  </div>
                  <div class="doc-type-content">
                    <div class="doc-type-title">QA问答格式</div>
                    <div class="doc-type-desc">一问一答导入，准确性更佳</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="upload-area">
              <div class="upload-container">
                <el-upload
                  class="file-uploader"
                  style="width: 100%"
                  action="#"
                  :auto-upload="false"
                  :show-file-list="true"
                  :on-change="handleFileChange"
                  :on-remove="handleFileRemove"
                  :file-list="fileList"
                  :accept="currentFileAccept"
                  :before-upload="beforeFileUpload"
                  multiple
                  drag
                >
                  <div class="upload-placeholder">
                    <div class="upload-icon">
                      <i class="el-icon-upload"></i>
                    </div>
                    <div class="upload-text">
                      拖入文件 或 <span class="upload-link">点击上传</span>
                    </div>
                  </div>
                </el-upload>
              </div>
              <div class="file-tips">
                {{ currentFileType === 'noStructure' ? '支持 txt, pdf, md, docx格式文件' : '支持 csv 文件' }}
                <span v-if="currentFileType === 'qa'" class="download-template" @click="downloadTemplate">
                  | <a href="javascript:void(0)">下载模板</a>
                </span>
              </div>
            </div>
          </div>
          <div class="kb-selector-link">
            <el-button
              type="text"
              class="switch-to-kb-btn"
              @click="toggleUploadSection"
            >
              <i class="el-icon-collection"></i>
              绑定已有知识库
            </el-button>
          </div>
        </el-form-item>

        <el-dialog
          title="绑定知识库"
          :visible.sync="knowledgeDialogVisible"
          width="80%"
          :close-on-click-modal="false"
          append-to-body
          class="knowledge-dialog"
        >
          <div class="dialog-content">
            <div class="dialog-search">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索知识库"
                prefix-icon="el-icon-search"
                clearable
                @input="handleSearchChange"
                class="search-input"
              ></el-input>
            </div>

            <div class="knowledge-grid">
              <div
                v-for="item in filteredKnowledgeList"
                :key="item.id"
                class="knowledge-card"
                :class="{ 'selected': selectedKnowledgeIds.includes(item.id) }"
                @click="toggleKnowledgeSelection(item.id)"
              >
                <div class="card-header">
                  <div class="knowledge-icon">
                    <i class="el-icon-folder"></i>
                  </div>
                  <div class="selection-indicator">
                    <i class="el-icon-check" v-if="selectedKnowledgeIds.includes(item.id)"></i>
                  </div>
                </div>
                <div class="card-content">
                  <div class="knowledge-title">{{ item.name }}</div>
                  <div class="knowledge-description">
                    {{ item.description || "暂无描述" }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div slot="footer" class="dialog-footer">
            <div class="selected-count">
              已选择 {{ selectedKnowledgeIds.length }} 个知识库
            </div>
            <div class="footer-buttons">
              <el-button @click="knowledgeDialogVisible = false">取消</el-button>
              <el-button type="primary" @click="handleKnowledgeConfirm">
                确认绑定
              </el-button>
            </div>
          </div>
        </el-dialog>
      </template>

      <el-form-item class="form-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading" :disabled="loading">创建智能体</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { api } from "@/api/request";
import routerMixin from "@/mixins/routerMixin";
import { getNodesByAppType } from "@/utils/appTemplates";
import { EnumApplicationType } from "@/utils/enums";

export default {
  name: "CreateApp",
  mixins: [routerMixin],
  data() {
    return {
      appType: "light",
      knowledgeDialogVisible: false,
      selectedKnowledgeIds: [],
      selectedKnowledgeCodes: [],
      selectedKnowledge: null,
      selectedKnowledgeList: [],
      knowledgeList: [],
      fileList: [], // 文档文件列表
      currentFileType: "noStructure", // 当前选择的文件类型
      showUploadSection: false,
      loading: false, // 添加loading状态变量
      formData: {
        name: "",
        description: "",
        introduce: "",
        profilePhoto: "",
        isPublic: false,
        isTrueCustomerServer: false,
        applicationTypeCode: "",
        applicationTypeName: "",
        sessionFlowCode: this.appType === "light" ? "1" : "2",
        SessionFlowSetting: "",
        knowledgeBaseIds: "",
      },
      categories: EnumApplicationType,
      searchKeyword: "", // 搜索关键词
      rules: {
        name: [
          { required: true, message: "请输入智能体名称", trigger: "blur" },
          {
            min: 0,
            max: 50,
            message: "长度在 0 到 50 个字符",
            trigger: "blur",
          },
        ],
        description: [
          { required: true, message: "请输入智能体描述", trigger: "blur" },
          {
            min: 0,
            max: 500,
            message: "长度在 0 到 500 个字符",
            trigger: "blur",
          },
        ],
        introduce: [
          { required: true, message: "请输入开场介绍", trigger: "blur" },
          {
            min: 0,
            max: 2000,
            message: "长度在 0 到 2000 个字符",
            trigger: "blur",
          },
        ],
        profilePhoto: [
          { required: true, message: "请上传智能体头像", trigger: "blur" },
          {
            min: 0,
            max: 500,
            message: "长度在 0 到 500 个字符",
            trigger: "blur",
          },
        ],
        applicationTypeCode: [
          { required: true, message: "请选择智能体分类", trigger: "change" },
        ],
        applicationTypeName: [
          { required: true, message: "请选择智能体分类", trigger: "change" },
        ],
        SessionFlowSetting: [],
      },
    };
  },
  computed: {
    // 过滤知识库列表
    filteredKnowledgeList() {
      if (!this.searchKeyword) {
        return this.knowledgeList;
      }
      const keyword = this.searchKeyword.toLowerCase();
      return this.knowledgeList.filter(item =>
        item.name.toLowerCase().includes(keyword) ||
        (item.description && item.description.toLowerCase().includes(keyword))
      );
    },
    // 当前文档类型支持的文件格式
    currentFileAccept() {
      if (this.currentFileType === 'qa') {
        return '.csv';
      } else {
        return '.txt,.pdf,.md,.docx';
      }
    }
  },
  created() {
    // 页面创建时初始化数据
    this.refreshData();
  },
  methods: {
    // 添加刷新数据的方法
    refreshData() {
      // 重置表单数据
      this.formData = {
        name: "",
        description: "",
        introduce: "",
        profilePhoto: "",
        isPublic: false,
        isTrueCustomerServer: false,
        applicationTypeCode: "",
        applicationTypeName: "",
        sessionFlowCode: this.appType === "light" ? "1" : "2",
        SessionFlowSetting: "",
      };

      // 如果是知识智能体类型，重新获取知识库列表
      if (this.appType === "knowledge") {
        this.fetchKnowledgeList();
      }
    },
    selectAppType(type) {
      this.appType = type;
      this.formData.sessionFlowCode =
        type === "light" ? "1" : "2";
      if (type === "knowledge") {
        this.fetchKnowledgeList();
      }
    },
    async fetchKnowledgeList() {
      try {
        this.loading = true;
        // 调用api.rag.getKnowledgeList接口获取知识库列表
        const params = {
          keyword: "",
          pageIndex: 0,
          pageSize: 100,
        };
        const res = await api.rag.getKnowledgeList(params);
        if (res.code === 200) {
          this.knowledgeList = res.data.items || [];
          console.log("知识库列表获取成功:", this.knowledgeList);
        } else {
          this.$showFriendlyError(null, res.message || "获取知识库列表失败");
        }
      } catch (error) {
        console.error("获取知识库列表失败:", error);
        this.$showFriendlyError(null, "获取知识库列表失败");
      } finally {
        this.loading = false;
      }
    },
    showKnowledgeDialog() {
      this.knowledgeDialogVisible = true;
      if (this.knowledgeList.length === 0) {
        this.fetchKnowledgeList();
      }
    },
    handleKnowledgeConfirm() {
      this.selectedKnowledgeList = this.knowledgeList.filter((item) =>
        this.selectedKnowledgeIds.includes(item.id)
      );

      // 更新知识库codes
      this.selectedKnowledgeCodes = this.selectedKnowledgeList.map(
        (item) => item.code
      );
      console.log("选择的知识库Codes:", this.selectedKnowledgeCodes);

      this.selectedKnowledge = this.selectedKnowledgeList[0]; // 保留兼容性
      this.knowledgeDialogVisible = false;
    },
    // 选择文件类型
    selectFileType(type) {
      this.currentFileType = type;
    },
    // 文件上传前验证
    beforeFileUpload(file) {
      // 根据当前文档类型获取允许的文件格式
      const allowedFormats = this.getAllowedFileFormats();

      // 检查文件扩展名
      const fileName = file.name.toLowerCase();
      const isValidType = allowedFormats.extensions.some(ext => fileName.endsWith(ext));

      // 检查MIME类型
      const isValidMime = allowedFormats.mimeTypes.includes(file.type);

      // 检查文件大小 (30MB)
      const isValidSize = file.size / 1024 / 1024 <= 30;

      if (!isValidType && !isValidMime) {
        this.$showFriendlyError(null, allowedFormats.errorMessage);
        return false;
      }

      if (!isValidSize) {
        this.$showFriendlyError(null, '文件大小不能超过30MB');
        return false;
      }

      return true;
    },

    // 获取当前文档类型允许的文件格式
    getAllowedFileFormats() {
      if (this.currentFileType === 'qa') {
        return {
          extensions: ['.csv'],
          mimeTypes: ['text/csv', 'application/csv'],
          errorMessage: '请上传CSV格式文件'
        };
      } else {
        return {
          extensions: ['.txt', '.pdf', '.md', '.docx'],
          mimeTypes: [
            'text/plain',
            'application/pdf',
            'text/markdown',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          ],
          errorMessage: '请上传txt, pdf, md, docx格式文件'
        };
      }
    },

    // 处理文件选择变化
    handleFileChange(file, fileList) {
      this.fileList = fileList.slice(0, 30); // 限制最多30个文件
    },
    // 处理文件移除
    handleFileRemove(file, fileList) {
      this.fileList = fileList;
    },
    // 下载QA模板
    downloadTemplate() {
      const templateUrl = 'https://yuanzhiqi-test.obs.cn-southwest-2.myhuaweicloud.com/fileDemo/qa_template.csv';
      const link = document.createElement('a');
      link.href = templateUrl;
      link.download = 'qa_template.csv';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    async handleAIFill() {
      // 验证必填字段：智能体名称和智能体描述
      if (!this.formData.name.trim()) {
        this.$showFriendlyError(null, '请您填写智能体名称后才可使用此功能');
        return;
      }

      if (!this.formData.description.trim()) {
        this.$showFriendlyError(null, '请您填写智能体描述后才可使用此功能');
        return;
      }

      // 创建全页面加载状态
      const loadingInstance = this.$loading({
        lock: true,
        text: '正在帮您生成，请稍后...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
        customClass: 'ai-full-page-loading'
      });

      try {
        // 构建基础参数
        const baseParams = {
          agentName: this.formData.name.trim(),
          agentCategory: this.categories.find(cat => cat.value === this.formData.applicationTypeCode)?.label || "其他",
          model: "qwen-turbo"
        };

        // 并发调用两个接口
        const [introductionRes, settingsRes] = await Promise.all([
          // 调用开场介绍优化接口
          api.agentService.optimizeAgentDescription({
            ...baseParams,
            currentDescription: this.formData.description.trim(),
            require: `### **母版提示词：AI智能体开场白优化师**

              你是一位顶级的AI智能体对话体验优化专家，专注于打造亲切、清晰且能有效引导用户的对话开场白。

              #### **核心目标**
              你的任务是基于用户提供的 **[智能体名称]**、**[智能体描述]**、**[应用分类]** 和 **[当前智能体开场白]**，摒弃后者，并根据前三者信息，创作出一个全新的、高质量的开场介绍文案。

              #### **核心原则**
              生成的开场白必须遵循以下四个黄金原则：

              1.  **身份清晰 (Who I am):** 在开场白中明确介绍自己的身份或名字，建立认知。
              2.  **价值主张 (What I do for you):** 用一句话概括能为用户解决的核心问题或提供的核心价值，让用户秒懂你的用途。
              3.  **行动号召 (What you should do next):** 必须包含一个清晰、简单的指令或问题，引导用户开启对话，消除用户的输入疑虑。
              4.  **风格匹配 (How I sound):** 开场白的语气和风格必须与智能体的名称、描述和应用分类高度一致。（例如："法律顾问"应专业严谨，"旅游规划师"应热情活泼）。

              #### **工作流程**
              1.  **全面分析：** 仔细阅读并理解用户提供的\`[智能体名称]\`、\`[智能体描述]\`和\`[应用分类]\`，忽略\`[当前智能体开场白]\`的内容，只将其作为反面案例。
              2.  **确定风格：** 根据分析结果，确定最合适的对话风格。例如：
                  *   **办公效率类：** 专业、高效、直接。
                  *   **内容创作类：** 创意、活泼、鼓励。
                  *   **生活服务类：** 亲切、耐心、可靠。
                  *   **专业咨询类：** 严谨、权威、值得信赖。
              3.  **结构化构建：** 按照【问候语 + 自我介绍（身份）+ 功能说明（价值）+ 引导提问（行动）】的结构来组织语言。
              4.  **语言润色：** 对构建好的句子进行精炼，使其更加自然、流畅、友好。可以适当使用1-2个贴合角色的Emoji来增加亲和力，但避免过度使用。
              5.  **最终输出：** 直接呈现最终优化好的开场白文案。

              #### **输出要求**
              *   **直接交付：** 你的回答**必须且只能是**最终生成的那段开场白文本，不要包含任何"这是优化后的开场白："之类的额外解释。
              *   **简洁有力：** 文本长度建议控制在30-80字之间，确保信息密度高且易于阅读。
              *   **引导性强：** 必须以一个开放式问题或一个明确的指令结束。
              *   **完全原创：** 忽略用户提供的\`[当前智能体开场白]\`，基于前三个要素进行全新创作。`,
            text: ""
          }),
          // 调用智能体设定优化接口
          api.agentService.optimizeAgentDescription({
            ...baseParams,
            currentDescription: this.formData.description.trim(),
            require: `### **母版提示词：智能体提示词（Prompt）架构师**

              你是一位顶级的AI智能体提示词（Prompt）架构师和优化专家。

              你的核心任务是基于用户提供的 **[智能体描述]**、**[应用分类]** 和 **[当前提示词]**，生成一个结构化、规范化、高效且合规的全新提示词。新生成的提示词必须严格遵循【角色、目标、技能、工作流、注意、示例】这六个部分进行组织，以确保AI智能体能够稳定、高质量地执行任务。

              #### **核心技能**

              1.  **深度理解与分析：** 精准提炼用户输入信息中的核心需求、功能点和约束条件。
              2.  **结构化重构：** 将零散的需求和描述，重构成逻辑清晰、层次分明的结构化文本。
              3.  **Prompt工程专业知识：** 熟悉大型语言模型的工作原理，知道如何通过精确的指令（如角色扮演、思维链、提供示例等）来激发模型最佳性能。
              4.  **语言精炼：** 使用专业、精确、无歧义的语言，避免口语化和模糊不清的表达。
              5.  **创新性设计：** 在遵循规范的基础上，能够创造性地设计出清晰的工作流程和高质量的示例，使最终的提示词极具可用性。

              #### **工作流程**

              1.  **接收与解析：**
                  *   仔细阅读用户提供的三个输入：
                      *   \`[智能体描述]\`：这是关于智能体是什么、用来做什么的简要说明。
                      *   \`[应用分类]\`：这指明了智能体所属的领域，如"内容创作"、"代码生成"、"客户服务"等。
                      *   \`[当前提示词]\`：这是用户已有的、可能不规范或过于简单的原始提示词。

              2.  **提炼与映射：**
                  *   **角色 (Role):** 从 \`[智能体描述]\` 中提炼出智能体的核心身份和专业定位，用一句话清晰定义。例如，"一位资深的小红书爆款文案写手"。
                  *   **目标 (Goal):** 结合 \`[智能体描述]\` 和 \`[应用分类]\`，明确智能体需要完成的核心任务或解决的关键问题。
                  *   **技能 (Skills):** 分析 \`[当前提示词]\` 和 \`[智能体描述]\`，将智能体为完成目标所必须具备的具体能力，以列表形式分点阐述。
                  *   **工作流 (Workflow):** 这是最关键的一步。你需要根据目标和技能，设计一个清晰、可执行的步骤化流程。如果 \`[当前提示词]\` 中已有流程，请优化并使其更具逻辑性；如果 \`[当前提示词]\` 只是一个简单的命令，你需要为其创造一个完整的工作流。
                  *   **注意 (Attention):** 总结智能体在执行任务时必须遵守的规则、限制或风格要求。例如，输出格式、语言风格、禁止行为、信息来源等。
                  *   **示例 (Example):** 基于上述所有部分，创建一个或多个具体的"用户输入"和"智能体理想输出"的配对示例。这个示例应该能完美展示工作流的执行过程和最终产出质量，为AI提供一个清晰的模仿范本。

              3.  **整合与输出：**
                  *   将上述六个部分整合起来，形成一个完整、规范的提示词。
                  *   使用 \`##\` 作为每个部分的标题（例如 \`## 角色\`）。
                  *   直接输出这个全新的、结构化的提示词，无需任何额外的解释或开场白。

              #### **输出要求**

              *   **严格遵循格式：** 输出必须且只能包含【角色、目标、技能、工作流、注意、示例】六个部分，并使用指定的Markdown标题格式。
              *   **内容忠实于输入：** 生成的内容必须完全基于用户提供的三个输入进行优化和重构，不得凭空捏造核心功能。
              *   **专业与简洁：** 语言专业、书面化，内容简洁明了。
              *   **完整性：** 确保每个部分都内容详实，特别是"工作流"和"示例"部分，它们是提示词成功的关键。`,
            text: ""
          })
        ]);

        // 处理开场介绍结果
        if (introductionRes.isSuccess && introductionRes.data?.optimizedDescription) {
          this.formData.introduce = introductionRes.data.optimizedDescription;
        } else {
          console.warn('开场介绍生成失败:', introductionRes.message);
        }

        // 处理智能体设定结果
        if (settingsRes.isSuccess && settingsRes.data?.optimizedDescription) {
          this.formData.SessionFlowSetting = settingsRes.data.optimizedDescription;
        } else {
          console.warn('智能体设定生成失败:', settingsRes.message);
        }

        // 检查是否有任何内容生成成功
        if ((introductionRes.isSuccess && introductionRes.data?.optimizedDescription) ||
            (settingsRes.isSuccess && settingsRes.data?.optimizedDescription)) {
          this.$message.success('AI内容生成成功！请检查并根据需要进行调整。');
        } else {
          this.$showFriendlyError(null, 'AI生成失败，请稍后重试');
        }

      } catch (error) {
        console.error('AI一键填写失败:', error);
        this.$showFriendlyError(error, 'AI生成失败，请稍后重试');
      } finally {
        // 关闭全页面加载状态
        loadingInstance.close();
      }
    },


    goToTemplates() {
      // this.goWithRefresh("/discover/template");
      this.$message.info("为了给您更完美的体验，我们正在做最后的冲刺，很快解锁");
    },
    beforeAvatarUpload(file) {
      const isImage = file.type.startsWith("image/");
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isImage) {
        this.$showFriendlyError(null, "上传头像图片只能是图片格式!");
      }
      if (!isLt2M) {
        this.$showFriendlyError(null, "上传头像图片大小不能超过 2MB!");
      }
      return isImage && isLt2M;
    },
    async handleAvatarUpload(options) {
      const file = options.file;
      try {
        // 构建上传参数，与工作流头像上传保持一致
        const uploadParams = {
          file: file,
          fileType: "image",
          folder: "app_avatars",
        };
        // 直接使用upload方法上传文件
        const uploadRes = await api.oss.upload(uploadParams);

        if (uploadRes.data?.fileUrl) {
          // 设置头像URL
          this.formData.profilePhoto = uploadRes.data.fileUrl;
          this.$message.success("上传成功");
        } else {
          throw new Error(uploadRes.message || "上传失败");
        }
      } catch (error) {
        console.error("上传头像失败:", error);
        this.$showFriendlyError(null, "上传失败，请稍后重试");
      }
    },
    handleCancel() {
      this.goWithRefresh("/create/app");
    },
    async handleSubmit() {
              console.log("提交前检查智能体类型:", this.appType);
      console.log("提交前检查上传区域显示:", this.showUploadSection);
      console.log("提交前检查知识库IDs:", this.selectedKnowledgeIds);
      console.log("提交前检查知识库Codes:", this.selectedKnowledgeCodes);

              // 如果是知识智能体且选择了知识库绑定模式，把知识库code设置到表单中
      if (this.appType === "knowledge" && !this.showUploadSection) {
        // 使用知识库code而不是id
        this.formData.knowledgeBaseIds = this.selectedKnowledgeCodes.join(",");
        console.log(
          "设置knowledgeBaseIds(codes)到formData:",
          this.formData.knowledgeBaseIds
        );
      } else if (this.appType !== "knowledge" || this.showUploadSection) {
        // 非知识智能体或上传模式时，设置为空字符串
        this.formData.knowledgeBaseIds = "";
        console.log("非知识智能体或上传模式，设置空knowledgeBaseIds");
      }

      console.log("表单提交前的数据:", JSON.stringify(this.formData));

      // 使用普通表单验证，但忽略知识库相关字段
      this.$refs.form.validate((valid, invalidFields) => {
        console.log("表单验证结果:", valid);
        if (!valid) {
          console.log("表单验证失败字段:", JSON.stringify(invalidFields));
          return;
        }

        // 通过验证，开始提交表单
        this.submitForm();
      });
    },
    handleCategoryChange(value) {
      const category = this.categories.find((item) => item.value === value);
      if (category) {
        this.formData.applicationTypeName = category.label;
        this.formData.applicationTypeCode = category.value;
      }
    },
    toggleUploadSection() {
      this.showUploadSection = !this.showUploadSection;
    },
    toggleKnowledgeSelection(id) {
      if (this.selectedKnowledgeIds.includes(id)) {
        this.selectedKnowledgeIds = this.selectedKnowledgeIds.filter(
          (i) => i !== id
        );
      } else {
        this.selectedKnowledgeIds.push(id);
      }
    },
    showNewKnowledgeForm() {
      // 跳转到新建知识库页面
      this.goWithRefresh("/create/knowledge");
    },
    // 添加移除知识库标签的方法
    removeKnowledgeSelection(id) {
      this.selectedKnowledgeIds = this.selectedKnowledgeIds.filter(
        (i) => i !== id
      );

      // 更新知识库列表
      this.selectedKnowledgeList = this.selectedKnowledgeList.filter(
        (item) => item.id !== id
      );

      // 更新知识库codes
      this.selectedKnowledgeCodes = this.selectedKnowledgeList.map(
        (item) => item.code
      );
      console.log("移除后的知识库Codes:", this.selectedKnowledgeCodes);

      // 更新selectedKnowledge以保持兼容性
      this.selectedKnowledge = this.selectedKnowledgeList.length
        ? this.selectedKnowledgeList[0]
        : null;
    },
    /**
     * 组装应用数据
            * 使用模板文件，根据当前智能体类型和输入的配置组装完整的请求数据
     */
    assembleAppData() {
      // 获取节点结构模板
      const flowDetailInput = getNodesByAppType(this.appType);

              // 添加智能体名称到流程
      flowDetailInput.name = this.formData.name;

              // 如果是知识智能体且已选择知识库，更新知识库节点配置
      if (
        this.appType === "knowledge" &&
        this.selectedKnowledgeCodes.length > 0
      ) {
        if (
          flowDetailInput.knowledgeNodes &&
          flowDetailInput.knowledgeNodes.length > 0
        ) {
          const kbNode = flowDetailInput.knowledgeNodes[0];
          // 将知识库code用英文逗号隔开拼接
          kbNode.data.nodeTypeConfig.knowledgeBase =
            this.selectedKnowledgeCodes.join(",");
        }
      }

      // 设置大模型节点的系统提示词
      if (
        flowDetailInput.largeModelNodes &&
        flowDetailInput.largeModelNodes.length > 0
      ) {
        const modelNode = flowDetailInput.largeModelNodes[0];
        // 更新系统提示词为表单中的智能体设定
        modelNode.data.nodeTypeConfig.systemPrompt =
          this.formData.SessionFlowSetting;
      }
      flowDetailInput.humanTransferNodes=[]
      flowDetailInput.edges=[]

      // 构建完整请求数据
      const requestData = {
        name: this.formData.name,
        description: this.formData.description,
        introduce: this.formData.introduce || "有什么可以帮您?",
        profilePhoto: this.formData.profilePhoto,
        isPublic: this.formData.isPublic,
        isTrueCustomerServer: this.formData.isTrueCustomerServer || false,
        applicationType: this.formData.applicationTypeCode,
        sessionFlowCode: this.formData.sessionFlowCode,
        SessionFlowSetting: this.formData.SessionFlowSetting,
        createFlowDetailInput: flowDetailInput,
      };

      return requestData;
    },
    // 新增提交表单逻辑
    async submitForm() {
      // 防止重复提交
      if (this.loading) {
        return;
      }

      this.loading = true;
      try {
        // 组装应用数据，包含模板结构
        const requestData = this.assembleAppData();

        // 打印请求数据，用于调试
        console.log("创建智能体请求数据:", JSON.stringify(requestData));

        // 调用创建接口
        const res = await api.sessionFlow.create(requestData);
        if (res.code === 200) {
          this.$message.success("创建成功");
          // 成功后不重置loading状态，保持按钮禁用直到页面跳转
          this.goWithRefresh("/create/app");
          return; // 成功后直接返回，不执行finally中的loading重置
        }
      } catch (error) {
        console.error("创建智能体失败:", error);
        this.$message.error("创建失败，请重试");
        // 只有在出错时才重置loading状态，允许用户重试
        this.loading = false;
      }
    },
    // 处理搜索关键词变化
    handleSearchChange() {
      // 搜索输入变化时，直接通过computed重新计算filteredKnowledgeList
    },
  },
};
</script>

<style lang="scss" scoped>
.create-app-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  .create-app-header {
    margin-bottom: 20px;

    h2 {
      margin: 0;
      font-size: 24px;
    }
  }

  .app-type-selection {
    margin-bottom: 24px;

    .app-type-card {
      border: 1px solid #dcdfe6;
      border-radius: 8px;
      padding: 20px;
      cursor: pointer;
      transition: all 0.3s;
      display: flex;
      align-items: center;
      gap: 16px;
      background-color: #fff;

      &.active {
        border-color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
      }

      &:hover {
        border-color: var(--el-color-primary);
      }

      .app-type-icon {
        i {
          font-size: 24px;
          color: var(--el-color-primary);
        }
      }

      .app-type-content {
        h3 {
          margin: 0 0 8px;
          font-size: 16px;
          font-weight: 500;
        }

        p {
          margin: 0;
          color: #606266;
          font-size: 14px;
        }
      }
    }
  }

  .quick-actions {
    display: flex;
    gap: 24px;
    margin-bottom: 24px;

    .el-button {
      padding: 0;
      height: auto;
      font-size: 14px;

      i {
        margin-right: 4px;
      }
    }
  }

  .app-form {
    .el-form-item {
      margin-bottom: 24px;

      &__label {
        padding-bottom: 8px;
        font-size: 14px;
        color: #606266;
      }
    }

    // 左右布局样式调整
    &.el-form--label-left {
      .el-form-item__label {
        text-align: right;
        padding-right: 12px;
      }
    }

    .form-item-header {
      display: flex;
      align-items: center;
      gap: 4px;
      margin-bottom: 8px;

      .label-text {
        font-size: 14px;
        color: #606266;
      }

      i {
        color: #909399;
        cursor: pointer;
      }
    }

    .avatar-uploader {
      .upload-area {
        width: 100px;
        height: 100px;
        border: 1px dashed #d9d9d9;
        border-radius: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        &:hover {
          border-color: var(--el-color-primary);
        }

        i {
          font-size: 24px;
          color: #8c939d;
        }
      }

      img {
        width: 100px;
        height: 100px;
        object-fit: cover;
        border-radius: 8px;
      }
    }

    .switch-container {
      width: 100%;
    }

    .setting-container {
      width: 100%;
    }

    .switch-group {
      display: flex;
      align-items: center;
      gap: 8px;

      .switch-label {
        font-size: 14px;
        color: #606266;
      }
    }

    .form-tip {
      margin-top: 4px;
      font-size: 12px;
      color: #909399;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 32px;
    }
  }

  .knowledge-base-selection {
    display: flex;
    align-items: center;
    gap: 16px;

    .selected-knowledge {
      flex: 1;
      padding: 8px 12px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      min-height: 40px;
      display: flex;
      align-items: center;
      color: #606266;

      .knowledge-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        width: 100%;

        .knowledge-tag {
          margin-right: 0;
        }
      }
    }
  }

  .knowledge-dialog {
    .dialog-content {
      display: flex;
      flex-direction: column;
      gap: 20px;
      min-height: 400px;
    }

    .dialog-search {
      .search-input {
        width: 100%;

        :deep(.el-input__inner) {
          height: 40px;
          padding-left: 35px;
        }

        :deep(.el-input__prefix) {
          left: 8px;
        }
      }
    }

    .selected-count {
      font-size: 14px;
      color: #606266;
      flex: 1;
      text-align: left;
    }

    .knowledge-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 16px;
      // overflow-y: auto;
      max-height: 450px;
      padding-right: 8px;
    }

    .knowledge-card {
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      padding: 16px;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      gap: 12px;
      background-color: #fff;
      transition: all 0.3s ease;
      min-height: 120px;

      &:hover {
        border-color: #409eff;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
        transform: translateY(-2px);
      }

      &.selected {
        border-color: #409eff;
        background-color: #f0f9ff;
        box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.2);

        .card-header .selection-indicator {
          opacity: 1;
        }
      }

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .knowledge-icon {
          width: 32px;
          height: 32px;
          border-radius: 6px;
          background-color: #e1f3d8;
          display: flex;
          align-items: center;
          justify-content: center;

          i {
            font-size: 16px;
            color: #67c23a;
          }
        }

        .selection-indicator {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background-color: #67c23a;
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s;

          i {
            font-size: 12px;
            color: #fff;
          }
        }
      }

      .card-content {
        flex: 1;

        .knowledge-title {
          font-size: 15px;
          font-weight: 500;
          color: #303133;
          line-height: 1.4;
          margin-bottom: 6px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .knowledge-description {
          font-size: 13px;
          color: #909399;
          line-height: 1.5;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          min-height: 40px;
        }
      }
    }

    :deep(.el-dialog__header) {
      padding: 20px 20px 0;
      border-bottom: 1px solid #ebeef5;
      margin-bottom: 0;

      .el-dialog__title {
        font-size: 18px;
        font-weight: 500;
        color: #303133;
      }
    }

    :deep(.el-dialog__body) {
      padding: 20px;
    }

    :deep(.el-dialog__footer) {
      padding: 15px 20px 20px;
      border-top: 1px solid #ebeef5;

      .dialog-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
      }

      .footer-buttons {
        display: flex;
        gap: 12px;
      }

      .el-button {
        padding: 8px 20px;
        font-size: 14px;
      }
    }
  }

  // 新增样式 - 文档上传区域
  .document-upload {
    margin-top: 10px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 20px;
    background-color: #f9f9f9;

    .document-types {
      display: flex;
      flex-direction: column;
      margin-bottom: 20px;

      .doc-type-label {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 10px;
      }

      .doc-type-options {
        display: flex;
        gap: 20px;

        .doc-type-item {
          flex: 1;
          padding: 15px;
          border: 1px solid #dcdfe6;
          border-radius: 4px;
          cursor: pointer;
          background-color: #fff;
          display: flex;
          align-items: center;
          gap: 10px;
          transition: all 0.3s;

          &:hover {
            border-color: var(--el-color-primary);
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
          }

          &.active {
            border-color: var(--el-color-primary);
            background-color: var(--el-color-primary-light-9);
          }

          .doc-type-icon {
            i {
              font-size: 24px;
              color: var(--el-color-primary);
            }
          }

          .doc-type-content {
            .doc-type-title {
              font-size: 16px;
              font-weight: 500;
              margin-bottom: 5px;
            }

            .doc-type-desc {
              font-size: 12px;
              color: #909399;
            }
          }
        }
      }
    }

    .upload-area {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      background-color: #fafafa;
      margin-bottom: 10px;

      .upload-container {
        .file-uploader {
          width: 100%;

          .upload-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 150px;
            cursor: pointer;
            position: relative;
            transition: border-color 0.3s;

            .upload-icon {
              i {
                font-size: 48px;
                color: #c0c4cc;
                margin-bottom: 10px;
              }
            }

            .upload-text {
              font-size: 14px;
              color: #606266;
              text-align: center;

              .upload-link {
                color: var(--el-color-primary);
                font-style: normal;
                text-decoration: underline;
              }
            }
          }
        }
      }

      .file-tips {
        padding: 10px 20px;
        font-size: 12px;
        color: #909399;
        line-height: 1.5;
        border-top: 1px dashed #d9d9d9;
        background-color: #fff;
        border-bottom-left-radius: 6px;
        border-bottom-right-radius: 6px;

        .download-template {
          a {
            color: #409eff;
            text-decoration: none;
            cursor: pointer;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
    }
  }

  .direct-upload {
    margin-top: 12px;

    .direct-upload-btn {
      padding: 0;
      height: auto;
      font-size: 14px;
      color: #409eff;

      i {
        margin-right: 4px;
      }
    }
  }

  .kb-selector-link {
    margin-top: 12px;

    .switch-to-kb-btn {
      padding: 0;
      height: auto;
      font-size: 14px;
      color: #409eff;

      i {
        margin-right: 4px;
      }
    }
  }

  .form-item-tip {
    font-size: 12px;
    color: #909399;
    margin-bottom: 8px;
  }

  .introduce-example {
    font-size: 12px;
    color: #606266;
    margin-bottom: 16px;
    padding: 8px;
    background-color: #f9f9fa;
    border-radius: 4px;
    border-left: 3px solid #409eff;
  }
}

// AI全页面加载样式
:deep(.ai-full-page-loading) {
  .el-loading-mask {
    background-color: rgba(0, 0, 0, 0.8) !important;
  }

  .el-loading-spinner {
    .el-icon-loading {
      font-size: 48px !important;
      color: #409eff !important;
      animation: ai-rotate 2s linear infinite;
    }

    .el-loading-text {
      font-size: 16px !important;
      color: #ffffff !important;
      margin-top: 16px;
      font-weight: 500;
      letter-spacing: 1px;
    }
  }
}

@keyframes ai-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
