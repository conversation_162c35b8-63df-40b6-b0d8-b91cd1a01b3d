<template>
  <div class="page">
    <router-view v-if="$route.params.id"></router-view>
    <div class="page-content" v-else>
      <div
        v-if="loading"
        class="loading-container"
        v-loading="loading"
        element-loading-background="rgba(255, 255, 255, 0.7)"
        element-loading-text="加载知识库中..."
      >
        <!-- 移除el-loading组件，改用v-loading指令 -->
      </div>
      <div v-else class="main-container">
        <!-- 页面头部 -->
        <div class="page-header">
          <span class="page-title">知识库</span>
          <div class="header-info">
            <span class="capacity-info">剩余容量：37.5mb</span>
            <span class="expand-link">扩容</span>
          </div>
          <div class="create-btn" @click="handleCreateKnowledge">
            <i class="create-icon">+</i>
            <span class="create-text">新建知识库</span>
          </div>
        </div>

        <!-- 搜索框 -->
        <div class="search-container">
          <div class="search-box">
            <i class="search-icon"></i>
            <el-input
              v-model="searchKeyword"
              placeholder="搜索知识库"
              clearable
              @input="handleSearchChange"
              class="search-input"
            ></el-input>
          </div>
        </div>

        <!-- 知识库卡片瀑布流 -->
        <div class="knowledge-grid">
          <div
            v-for="knowledge in filteredKnowledgeList"
            :key="knowledge.id"
            class="knowledge-card"
            @click="handleSettingClick(knowledge)"
          >
            <!-- 卡片头部 -->
            <div class="card-header">
              <div class="knowledge-avatar"></div>
              <div class="knowledge-info">
                <div class="knowledge-title">{{ knowledge.name }}</div>
                <div class="knowledge-description">
                  {{ knowledge.description || "暂无描述" }}
                </div>
              </div>
            </div>

            <!-- 应用引用信息 -->
            <div class="app-reference">应用引用 3</div>

            <!-- 分割线 -->
            <div class="divider"></div>

            <!-- 卡片底部 -->
            <div class="card-footer">
              <span class="update-time">最近更新 {{ formatTime(knowledge.updateTime) }}</span>
              <div class="edit-btn" @click.stop="handleSettingClick(knowledge)">
                <span class="edit-text">编辑</span>
              </div>
              <div class="more-actions">
                <div class="action-dots"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建知识库弹窗 -->
    <el-dialog
      title="创建知识库"
      :visible.sync="createDialogVisible"
      width="16rem"
      :close-on-click-modal="false"
      append-to-body
      @close="handleDialogClose"
    >
      <el-form
        :model="createForm"
        ref="createForm"
        :rules="rules"
        label-position="top"
        @submit.native.prevent
      >
        <el-form-item label="知识库名称" prop="name" class="form-item">
          <el-input
            v-model="createForm.name"
            placeholder="给你的知识库取一个名字吧"
            maxlength="20"
            show-word-limit
          ></el-input>
        </el-form-item>

        <el-form-item label="知识库描述" prop="description" class="form-item">
          <el-input
            type="textarea"
            v-model="createForm.description"
            placeholder="请输入知识库描述"
            :rows="4"
          ></el-input>
        </el-form-item>

        <div class="dialog-tip">
          <i class="el-icon-info"></i>
          知识库需绑定到知识智能体才可生效
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">取消</el-button>
        <el-button type="primary" @click="handleCreateConfirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { api } from "@/api/request"
import EventBus from "@/utils/eventBus"

export default {
  name: "CreateKnowledge",
  data() {
    return {
      loading: true,
      createDialogVisible: false,
      searchKeyword: "",
      createForm: {
        name: "",
        description: "",
        userid: "",
      },
      rules: {
        name: [
          { required: true, message: "请输入知识库名称", trigger: "blur" },
          { max: 20, message: "长度不能超过20个字符", trigger: "blur" },
        ],
      },
      knowledgeList: [],
    };
  },
  computed: {
    // 过滤知识库列表
    filteredKnowledgeList() {
      if (!this.searchKeyword) {
        return this.knowledgeList;
      }
      const keyword = this.searchKeyword.toLowerCase();
      return this.knowledgeList.filter(item =>
        item.name.toLowerCase().includes(keyword) ||
        (item.description && item.description.toLowerCase().includes(keyword))
      );
    }
  },
  created() {
    this.fetchKnowledgeList();
    // 监听刷新事件
    EventBus.$on("refresh-knowledge-list", this.fetchKnowledgeList);
  },
  beforeDestroy() {
    // 组件销毁前移除事件监听
    EventBus.$off("refresh-knowledge-list", this.fetchKnowledgeList);
  },
  methods: {
    fetchKnowledgeList() {
      // 加载状态
      this.loading = true;

      // 查询参数 - 对于GET请求，这些会作为URL参数传递
      const params = {
        keyword: "",
        pageIndex: 0,
        pageSize: 100,
      };

      // 调用API获取知识库列表
      api.rag
        .getKnowledgeList(params)
        .then((res) => {
          if (res.code === 200) {
            // 处理返回数据
            if (res.data && res.data.items && res.data.items.length > 0) {
              // 将API返回的数据映射为组件需要的结构
              this.knowledgeList = res.data.items.map((item) => ({
                id: item.id,
                name: item.name,
                description: item.description,
                code: item.code,
                createTime: item.create_time,
                updateTime: item.update_time,
              }));
            } else {
              this.knowledgeList = []; // 没有数据时设为空数组
            }
          } else {
            this.$showFriendlyError({ message: res.status?.message }, "获取知识库列表失败");
            // 保持默认示例数据用于展示
          }
        })
        .catch((err) => {
          this.$showFriendlyError(err, "获取知识库列表失败，请重试");
          // 保持默认示例数据用于展示
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleCreateKnowledge() {
      this.createDialogVisible = true;
    },
    handleSettingClick(knowledge) {
      this.$router.push(`/create/knowledge/${knowledge.id}/setting`);
    },
    handleDialogClose() {
      this.createDialogVisible = false;
      this.$refs.createForm?.resetFields();
    },
    handleCreateConfirm() {
      this.$refs.createForm.validate((valid) => {
        if (valid) {
          // 创建知识库请求参数
          const params = {
            name: this.createForm.name,
            description: this.createForm.description,
          };

          // 发起创建知识库请求
          this.loading = true;
          api.rag
            .createKnowledge(params)
            .then((res) => {
              if (res.isSuccess || res.success) {
                this.$message.success("知识库创建成功");
                // 刷新知识库列表
                this.fetchKnowledgeList();
              } else {
                this.$showFriendlyError({ message: res.message }, "创建知识库失败");
              }
            })
            .catch((err) => {
              this.$showFriendlyError(null,
                "创建知识库失败：" + (err.message || "未知错误")
              );
            })
            .finally(() => {
              this.loading = false;
              this.createDialogVisible = false;
              this.$refs.createForm.resetFields();
            });
        }
      });
    },
    formatTime(time) {
      if (!time) return "2025-07-10 14:20:30";
      // 这里可以添加时间格式化逻辑
      return "2025-07-10 14:20:30";
    },
    // 处理搜索关键词变化
    handleSearchChange() {
      // 搜索输入变化时，直接通过computed重新计算filteredKnowledgeList
    }
  },
};
</script>

<style lang="scss" scoped>
.page {
  background-color: rgba(242, 246, 252, 1);
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.page-content {
  width: 100%;
  height: 100%;
}

.main-container {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 0.32rem; // 12px转换为rem
  height: calc(100vh);
  border: 0.013rem solid rgba(235, 236, 241, 1); // 0.5px转换为rem
  position: relative;
  overflow-y: auto;
}

.page-header {
  display: flex;
  align-items: center;
  width: calc(100% - 2.13rem); // 80px转换为rem
  height: 0.96rem; // 36px转换为rem
  margin: 0.8rem 0 0 1.07rem; // 30px 0 0 40px转换为rem
}

.page-title {
  font-size: 0.64rem; // 24px转换为rem
  font-family: PingFangSC-Medium;
  font-weight: 500;
  color: rgba(0, 0, 0, 1);
  line-height: 0.88rem; // 33px转换为rem
  margin-right: 0.43rem; // 16px转换为rem
}

.header-info {
  display: flex;
  align-items: center;
  margin-right: auto;
}

.capacity-info {
  font-size: 0.37rem; // 14px转换为rem
  color: rgba(102, 102, 102, 1);
  line-height: 0.53rem; // 20px转换为rem
}

.expand-link {
  font-size: 0.37rem; // 14px转换为rem
  color: rgba(37, 109, 255, 1);
  line-height: 0.53rem; // 20px转换为rem
  margin-left: 0.21rem; // 8px转换为rem
  cursor: pointer;
}

.create-btn {
  background-color: rgba(37, 109, 255, 1);
  border-radius: 0.21rem; // 8px转换为rem
  width: 3.09rem; // 116px转换为rem
  height: 0.96rem; // 36px转换为rem
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.create-icon {
  color: rgba(255, 255, 255, 1);
  font-size: 0.37rem; // 14px转换为rem
  margin-right: 0.21rem; // 8px转换为rem
  font-style: normal;
}

.create-text {
  color: rgba(255, 255, 255, 1);
  font-size: 0.37rem; // 14px转换为rem
  line-height: 0.53rem; // 20px转换为rem
}

.search-container {
  width: 6.4rem; // 240px转换为rem
  height: 0.96rem; // 36px转换为rem
  margin: 0.83rem 0 0 1.07rem; // 31px 0 0 40px转换为rem
}

.search-box {
  background-color: rgba(244, 246, 248, 1);
  border-radius: 0.21rem; // 8px转换为rem
  width: 6.4rem; // 240px转换为rem
  height: 0.96rem; // 36px转换为rem
  display: flex;
  align-items: center;
  padding: 0 0.35rem; // 13px转换为rem
  position: relative;
}

.search-icon {
  width: 0.4rem; // 15px转换为rem
  height: 0.43rem; // 16px转换为rem
  background: url('@/assets/layouts/icon-search.png') no-repeat center;
  background-size: contain;
  margin-right: 0.32rem; // 12px转换为rem
  position: absolute;
  left: 0.35rem; // 13px转换为rem
  z-index: 1;
}

.search-input {
  width: 100%;

  ::v-deep .el-input__inner {
    height: 0.96rem; // 36px转换为rem
    padding-left: 0.93rem; // 35px转换为rem
    background: transparent;
    border: none;
    font-size: 0.37rem; // 14px转换为rem
    color: rgba(186, 186, 186, 1);

    &:focus {
      color: #303133;
    }
  }

  ::v-deep .el-input__inner::placeholder {
    color: rgba(186, 186, 186, 1);
  }
}

.knowledge-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr); // 采用与智能体页面相同的3列布局
  gap: 0.53rem; // 20px转换为rem
  width: calc(100% - 2.13rem); // 80px转换为rem
  margin: 0.85rem 0 0 1.07rem; // 32px 0 0 40px转换为rem
  padding-bottom: 1.07rem; // 40px转换为rem

  // 响应式布局 - 与智能体页面保持一致
  @media (max-width: 42.67rem) { // 1600px转换为rem
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 32rem) { // 1200px转换为rem
    grid-template-columns: 1fr;
  }
}

.knowledge-card {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 0.21rem; // 8px转换为rem
  width: 100%; // 改为100%自适应宽度，而不是固定宽度
  height: 4.35rem; // 163px转换为rem
  border: 0.013rem solid rgba(235, 236, 241, 1); // 0.5px转换为rem
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 0.11rem 0.32rem rgba(0, 0, 0, 0.1); // 4px 12px转换为rem
  }
}

.card-header {
  display: flex;
  align-items: flex-start;
  width: calc(100% - 0.86rem); // 自适应宽度，减去左右边距
  height: 1.23rem; // 46px转换为rem
  margin: 0.43rem 0.43rem 0 0.43rem; // 16px转换为rem，增加右边距
}

.knowledge-avatar {
  border-radius: 50%;
  background-image: url('@/assets/knowledge/knowledge.png');
  background-size: cover;
  width: 1.17rem; // 44px转换为rem
  height: 1.17rem; // 44px转换为rem
  flex-shrink: 0; // 防止头像被压缩
}

.knowledge-info {
  flex: 1; // 自适应剩余宽度
  height: 1.23rem; // 46px转换为rem
  margin-left: 0.32rem; // 12px转换为rem
  min-width: 0; // 允许内容溢出处理
}

.knowledge-title {
  width: 100%; // 自适应宽度
  height: 0.59rem; // 22px转换为rem
  font-size: 0.43rem; // 16px转换为rem
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  color: rgba(0, 0, 0, 1);
  line-height: 0.59rem; // 22px转换为rem
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.knowledge-description {
  width: 100%; // 自适应宽度
  height: 0.53rem; // 20px转换为rem
  font-size: 0.32rem; // 12px转换为rem
  color: rgba(136, 136, 136, 1);
  line-height: 0.53rem; // 20px转换为rem
  margin-top: 0.11rem; // 4px转换为rem
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.app-reference {
  width: auto; // 自适应宽度
  height: 0.53rem; // 20px转换为rem
  font-size: 0.32rem; // 12px转换为rem
  color: rgba(186, 186, 186, 1);
  line-height: 0.53rem; // 20px转换为rem
  margin: 0.27rem 0 0 1.92rem; // 10px 0 0 72px转换为rem (保持与头像对齐)
}

.divider {
  width: calc(100% - 0.86rem); // 自适应宽度，减去左右边距
  height: 0.027rem; // 1px转换为rem
  background-color: rgba(235, 236, 241, 1);
  margin: 0.4rem 0.43rem 0 0.43rem; // 15px 0 0 16px转换为rem，增加右边距
}

.card-footer {
  display: flex;
  align-items: center;
  width: calc(100% - 0.86rem); // 自适应宽度，减去左右边距
  height: 0.8rem; // 30px转换为rem
  margin: 0.32rem 0.43rem 0.32rem 0.43rem; // 12px转换为rem，增加右边距
}

.update-time {
  flex: 1; // 自适应宽度，占据剩余空间
  height: 0.53rem; // 20px转换为rem
  font-size: 0.32rem; // 12px转换为rem
  color: rgba(186, 186, 186, 1);
  line-height: 0.53rem; // 20px转换为rem
  margin-top: 0.13rem; // 5px转换为rem
}

.edit-btn {
  background-color: rgba(244, 246, 248, 1);
  border-radius: 0.11rem; // 4px转换为rem
  height: 0.8rem; // 30px转换为rem
  width: 1.39rem; // 52px转换为rem
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 0.21rem; // 减少左边距，让布局更紧凑
  cursor: pointer;
  transition: all 0.3s;
  flex-shrink: 0; // 防止按钮被压缩

  &:hover {
    background-color: #e1f3d8;
  }
}

.edit-text {
  width: 0.75rem; // 28px转换为rem
  height: 0.53rem; // 20px转换为rem
  font-size: 0.37rem; // 14px转换为rem
  color: rgba(0, 0, 0, 1);
  line-height: 0.53rem; // 20px转换为rem
}

.more-actions {
  width: 1.17rem; // 44px转换为rem
  height: 0.8rem; // 30px转换为rem
  margin-left: 0.21rem; // 8px转换为rem
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.action-dots {
  width: 0.8rem; // 30px转换为rem
  height: 0.8rem; // 30px转换为rem
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDQiIGhlaWdodD0iMzAiIHZpZXdCb3g9IjAgMCA0NCAzMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTAiIGN5PSIxNSIgcj0iMiIgZmlsbD0iIzk5OTk5OSIvPgo8Y2lyY2xlIGN4PSIyMiIgY3k9IjE1IiByPSIyIiBmaWxsPSIjOTk5OTk5Ii8+CjxjaXJjbGUgY3g9IjM0IiBjeT0iMTUiIHI9IjIiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+Cg==') no-repeat center;
}

.loading-container {
  width: 100%;
  height: 10.67rem; // 400px转换为rem
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .loading-text {
    color: #909399;
    margin-top: 3.2rem; // 120px转换为rem
    font-size: 0.37rem; // 14px转换为rem
  }
}

// 创建知识库弹窗样式
::v-deep .el-dialog {
  border-radius: 0.21rem; // 8px转换为rem

  .el-dialog__header {
    padding: 0.53rem 0.53rem 0.27rem; // 20px 20px 10px转换为rem
    margin-right: 0;
    border-bottom: 0.027rem solid #f0f0f0; // 1px转换为rem
  }

  .el-dialog__body {
    padding: 0.64rem 0.53rem; // 24px 20px转换为rem
  }

  .el-dialog__footer {
    padding: 0.27rem 0.53rem 0.53rem; // 10px 20px 20px转换为rem
    border-top: 0.027rem solid #f0f0f0; // 1px转换为rem
  }
}

.form-item {
  margin-bottom: 0.64rem; // 24px转换为rem

  ::v-deep .el-form-item__label {
    padding-bottom: 0.21rem; // 8px转换为rem
    line-height: 1;
    color: #606266;
  }
}

.dialog-tip {
  display: flex;
  align-items: center;
  gap: 0.21rem; // 8px转换为rem
  padding: 0.32rem 0.43rem; // 12px 16px转换为rem
  background-color: #f5f7fa;
  border-radius: 0.11rem; // 4px转换为rem
  color: #909399;
  font-size: 0.37rem; // 14px转换为rem

  i {
    color: var(--el-color-primary);
  }
}
</style>
