<template>
  <div class="create-plugin-page">
    <router-view
      v-if="$route.params.id || $route.path.includes('/new')"
    ></router-view>
    <div class="page-content" v-else>
      <div class="plugin-sections">
        <!-- 自定义插件区域 -->
        <div class="plugin-section">
          <div class="section-header">
            <h2 class="section-title">自定义插件</h2>
          </div>
          <div
            class="plugin-container"
            v-loading="customLoading"
            element-loading-text="加载自定义插件中..."
          >
            <!-- 插件卡片列表 -->
            <div
              v-for="(plugin, index) in pluginList"
              :key="plugin.id"
              class="plugin-card"
              :style="{ '--card-index': index }"
              @click="handleEditPlugin(plugin)"
            >
              <div class="plugin-card-header">
                <div class="plugin-info">
                  <img
                    v-if="plugin.headSculpture"
                    :src="plugin.headSculpture"
                    class="plugin-icon"
                  />
                  <i v-else class="el-icon-connection"></i>
                  <div class="plugin-title">
                    <h3>{{ plugin.name }}</h3>
                    <p class="plugin-type">{{ plugin.type }}</p>
                  </div>
                </div>
              </div>
              <p class="plugin-desc">{{ plugin.description }}</p>
              <div class="plugin-footer">
                <el-tooltip effect="dark" :content="plugin.url" placement="bottom">
                  <span class="plugin-url">{{ plugin.url }}</span>
                </el-tooltip>
              </div>
            </div>

            <!-- 创建插件卡片 -->
            <div class="create-card" @click="handleCreatePlugin">
              <i class="el-icon-plus"></i>
              <span>创建插件</span>
            </div>
          </div>
        </div>

        <!-- 内置插件区域 -->
        <div class="plugin-section">
          <div class="section-header">
            <h2 class="section-title">内置插件</h2>
            <el-button
              type="primary"
              size="small"
              @click="showContactDialog = true"
            >
              联系客服上架插件
            </el-button>
          </div>

          <!-- 内置插件分类选择 -->
          <div class="builtin-categories">
            <el-radio-group
              v-model="activeBuiltinTab"
              size="small"
              @change="handleBuiltinTabChange"
            >
              <el-radio-button label="all">全部</el-radio-button>
              <el-radio-button label="basic">基础工具</el-radio-button>
              <el-radio-button label="search">搜索工具</el-radio-button>
              <el-radio-button label="content">内容工具</el-radio-button>
              <el-radio-button label="image">图像工具</el-radio-button>
              <el-radio-button label="media">音视频工具</el-radio-button>
              <el-radio-button label="other">其他</el-radio-button>
            </el-radio-group>
          </div>

          <div
            class="plugin-container"
            v-loading="builtinLoading"
            element-loading-text="加载内置插件中..."
          >
            <!-- 内置插件卡片列表 -->
            <div
              v-for="(plugin, index) in builtinPluginList"
              :key="plugin.id"
              class="plugin-card"
              :style="{ '--card-index': index }"
              @click="handleViewBuiltinPlugin(plugin)"
            >
              <div class="plugin-card-header">
                <div class="plugin-info">
                  <img
                    v-if="plugin.iconUrl || plugin.headSculpture"
                    :src="plugin.iconUrl || plugin.headSculpture"
                    class="plugin-icon"
                  />
                  <i v-else class="el-icon-connection"></i>
                  <div class="plugin-title">
                    <h3>{{ plugin.name }}</h3>
                  </div>
                </div>
              </div>
              <p class="plugin-desc">
                {{ plugin.shortDesc || plugin.description }}
              </p>
              <div class="plugin-footer builtin-plugin">
                <el-tag
                  v-for="tag in (plugin.tags || [])"
                  :key="tag"
                  type="primary"
                  size="mini"
                >
                  {{ getPluginCategoryLabel(tag) }}
                </el-tag>
              </div>
            </div>

            <!-- 空状态 -->
            <div
              v-if="!builtinLoading && builtinPluginList.length === 0"
              class="empty-content"
            >
              <i class="el-icon-box empty-icon"></i>
              <p class="empty-text">暂无{{ activeBuiltinTab === 'all' ? '内置' : getBuiltinTabLabel() }}插件</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 插件配置弹窗 -->
    <el-dialog
      title="插件配置"
      :visible.sync="showPluginConfigDialog"
      width="480px"
      :before-close="handleClosePluginConfigDialog"
    >
      <div class="plugin-config-content" v-if="selectedPlugin">
        <div class="plugin-info-header">
          <div class="plugin-icon-wrapper">
            <img
              v-if="selectedPlugin.iconUrl || selectedPlugin.headSculpture"
              :src="selectedPlugin.iconUrl || selectedPlugin.headSculpture"
              class="plugin-icon"
              alt="插件图标"
            />
            <i v-else class="el-icon-connection plugin-icon-default"></i>
          </div>
          <h3 class="plugin-name">{{ selectedPlugin.name }}</h3>
        </div>

        <div class="plugin-details">
          <div class="detail-item">
            <span class="detail-label">插件描述：</span>
            <span class="detail-value">{{
              selectedPlugin.shortDesc || "暂无描述"
            }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">插件说明：</span>
            <span class="detail-value">{{
              selectedPlugin.description || "暂无说明"
            }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">作者：</span>
            <span class="detail-value">{{
              selectedPlugin.nickName || "未知作者"
            }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">价格：</span>
            <span class="detail-value">{{
              selectedPlugin.pluginPoint || "暂无定价"
            }}</span>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleConfirmPluginConfig"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 联系客服弹窗 -->
    <el-dialog
      title="联系客服"
      :visible.sync="showContactDialog"
      width="400px"
      :before-close="handleCloseDialog"
    >
      <div class="contact-content">
        <div class="contact-item">
          <h4>客服电话</h4>
          <p class="phone-number">18848457100</p>
        </div>
        <div class="contact-item">
          <h4>客服企微二维码</h4>
          <div class="qr-code">
            <img src="/images/largemodel/customer.png" alt="客服企微二维码" />
            <p class="qr-tip">扫码添加客服微信</p>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showContactDialog = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { api } from "@/api/request"

export default {
  name: "CreatePluginPage",
  data() {
    return {
      pluginList: [], // 自定义插件列表
      builtinPluginList: [], // 内置插件列表（经过过滤的）
      allBuiltinPlugins: [], // 所有内置插件数据
      allPluginsData: [], // 存储所有从/api/v1/Plugin/list获取的数据
      customLoading: false, // 自定义插件加载状态
      builtinLoading: false, // 内置插件加载状态
      showContactDialog: false,
      showPluginConfigDialog: false, // 插件配置弹窗显示状态
      selectedPlugin: null, // 当前选中的插件
      activeBuiltinTab: "all",
    };
  },
  created() {
    this.fetchAllPluginData();
  },
  methods: {
    // 获取所有插件数据
    async fetchAllPluginData() {
      // 并行加载两个接口的数据
      await Promise.all([this.fetchCustomPluginList(), this.fetchPluginList()]);
    },

    // 获取自定义插件列表（保持原有接口不变）
    async fetchCustomPluginList() {
      try {
        this.customLoading = true;
        const params = {
          skipCount: 0,
          maxResultCount: 100,
          category: 2, // 自定义插件
        };
        const res = await api.apiPlugin.getList(params);
        if (res.isSuccess) {
          this.pluginList = res.data.items.map((item) => ({
            id: item.id,
            name: item.name,
            type: item.httpTypeCode,
            description: item.description,
            headSculpture: item.headSculpture,
            url: item.url,
            headers: item.headers,
            parameters: item.parameters,
            responseFields: item.responseFields,
          }));
        }
      } catch (error) {
        console.error("获取自定义插件列表失败:", error);
      } finally {
        this.customLoading = false;
      }
    },

    // 获取插件列表（新接口）
    async fetchPluginList() {
      try {
        this.builtinLoading = true;
        const params = {
          skipCount: 0,
          maxResultCount: 1000, // 获取更多数据用于前端过滤
        };
        const res = await api.plugin.getList(params);
        if (res.isSuccess) {
          this.allPluginsData = res.data.items || [];

          // 处理数据分类
          this.processPluginData();
        }
      } catch (error) {
        console.error("获取插件列表失败:", error);
      } finally {
        this.builtinLoading = false;
      }
    },

    // 处理插件数据分类
    processPluginData() {
      const customPlugins = [];
      const builtinPlugins = [];

      this.allPluginsData.forEach((item) => {
        const plugin = {
          id: item.id,
          name: item.name,
          type: item.httpTypeCode || item.type || "HTTP",
          description: item.description,
          shortDesc: item.shortDesc, // 保留短描述
          headSculpture: item.headSculpture,
          iconUrl: item.iconUrl, // 保留图标URL
          nickName: item.nickName, // 保留昵称
          author: item.author, // 保留作者
          pluginPoint: item.pluginPoint, // 保留价格
          url: item.url,
          headers: item.headers,
          parameters: item.parameters,
          responseFields: item.responseFields,
          tags: item.tags || [], // 改为tags数组
          category: item.category,
        };

        // tags包含1且category为2的数据放到自定义插件数组中
        if ((item.tags || []).includes(1) && item.category === 2) {
          customPlugins.push(plugin);
        } else {
          // 其他的放在内置插件数组中
          builtinPlugins.push(plugin);
        }
      });

      // 将新接口的自定义插件合并到现有的自定义插件列表中
      this.pluginList = [...this.pluginList, ...customPlugins];

      // 设置内置插件列表，并应用当前分类过滤
      this.allBuiltinPlugins = builtinPlugins;
      this.filterBuiltinPlugins();
    },

    // 过滤内置插件（前端过滤）
    filterBuiltinPlugins() {
      if (this.activeBuiltinTab === "all") {
        this.builtinPluginList = this.allBuiltinPlugins || [];
      } else {
        const targetTag = this.getTagByTab(this.activeBuiltinTab);
        this.builtinPluginList = (this.allBuiltinPlugins || []).filter(
          (plugin) => (plugin.tags || []).includes(targetTag)
        );
      }
    },

    // 根据tab获取对应的tag值（这里需要根据图片中的枚举来配置）
    getTagByTab(tab) {
      const tagMap = {
        basic: 2, // 基础工具
        search: 3, // 搜索工具
        content: 4, // 内容工具
        image: 5, // 图像工具
        media: 6, // 音视频工具
        other: 99, // 其他
      };
      return tagMap[tab] || null;
    },
    handleCreatePlugin() {
      this.$router.push("/create/plugin/new");
    },
    handleEditPlugin(plugin) {
      this.$router.push(`/create/plugin/edit/${plugin.id}`);
      console.log(plugin);
    },
    handleCloseDialog() {
      this.showContactDialog = false;
    },
    // 切换内置插件分类时进行前端过滤
    handleBuiltinTabChange() {
      this.filterBuiltinPlugins();
    },

    // 查看内置插件详情
    handleViewBuiltinPlugin(plugin) {
      // 如果插件是MCPl插件，跳转到编辑页面（查看模式）
      if (plugin.tags.includes(1)) {
        this.$router.push(`/create/plugin/view/${plugin.id}`);
      } else {
        // 其他类型的内置插件显示配置弹窗
        this.selectedPlugin = plugin;
        this.showPluginConfigDialog = true;
      }
    },

    // 关闭插件配置弹窗
    handleClosePluginConfigDialog() {
      this.showPluginConfigDialog = false;
      this.selectedPlugin = null;
    },

    // 确定插件配置
    handleConfirmPluginConfig() {
      this.showPluginConfigDialog = false;
      this.selectedPlugin = null;
      // 这里可以添加后续的插件配置逻辑
    },

    getBuiltinTabLabel() {
      const tabLabels = {
        all: "全部",
        basic: "基础工具",
        search: "搜索工具",
        content: "内容工具",
        image: "图像工具",
        media: "音视频工具",
        other: "其他",
      };
      return tabLabels[this.activeBuiltinTab] || "全部";
    },

    // 获取插件类型对应的中文标签
    getPluginTypeLabel(type) {
      const typeLabels = {
        1: "MCP插件", // 额外增加的MCP
        2: "基础工具", // 基础工具 = 2
        3: "搜索工具", // 搜索工具 = 3
        4: "内容工具", // 内容工具 = 4
        5: "图像工具", // 图像工具 = 5
        6: "音视频工具", // 音视频工具 = 6
        99: "其他", // 其他 = 99
      };
      return typeLabels[type] || typeLabels[parseInt(type)] || "其他插件";
    },

    // 获取插件分类对应的中文标签（基于tag字段）
    getPluginCategoryLabel(tag) {
      const categoryLabels = {
        1: "MCP插件", // 额外增加的MCP
        2: "基础工具", // 基础工具 = 2
        3: "搜索工具", // 搜索工具 = 3
        4: "内容工具", // 内容工具 = 4
        5: "图像工具", // 图像工具 = 5
        6: "音视频工具", // 音视频工具 = 6
        99: "其他", // 其他 = 99
      };
      return categoryLabels[tag] || categoryLabels[parseInt(tag)] || "其他插件";
    },
  },
};
</script>

<style lang="scss" scoped>
.create-plugin-page {
  width: 100%;
  height: 100%;
  padding: 24px;
  box-sizing: border-box;
  background-color: #f5f7fa;

  .page-content {
    width: 100%;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .plugin-sections {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .plugin-section {
    width: 100%;
    margin-bottom: 24px;

    .section-header {
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .section-title {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
        line-height: 1.5;
      }
    }

    .plugin-container {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      gap: 20px;
      min-height: 160px;
    }

    .empty-content {
      grid-column: 1 / -1; /* 跨越所有grid列 */
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 80px 0;
      color: #9ca3af;
      font-size: 14px;
      min-height: 200px; /* 确保有足够的高度居中显示 */

      .empty-icon {
        font-size: 64px;
        color: #d1d5db;
        margin-bottom: 20px;
      }

      .empty-text {
        margin: 0;
        color: #6b7280;
        font-size: 16px;
        line-height: 1.5;
        font-weight: 500;
      }
    }

    .builtin-categories {
      margin-bottom: 20px;

      :deep(.el-radio-group) {
        .el-radio-button {
          &:first-child .el-radio-button__inner {
            border-top-left-radius: 6px;
            border-bottom-left-radius: 6px;
          }

          &:last-child .el-radio-button__inner {
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;
          }

          .el-radio-button__inner {
            border: 1px solid #dcdfe6;
            border-left: 0;
            background-color: #fff;
            color: #606266;
            font-size: 14px;
            padding: 8px 16px;
            transition: all 0.3s ease;

            &:hover {
              background-color: #f5f7fa;
              color: #409eff;
            }
          }

          &:first-child .el-radio-button__inner {
            border-left: 1px solid #dcdfe6;
          }

          &.is-active .el-radio-button__inner {
            background-color: #409eff;
            border-color: #409eff;
            color: #fff;
            box-shadow: none;
          }
        }
      }
    }

    .plugin-card {
      position: relative;
      width: 100%;
      height: 160px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.05);
      cursor: pointer;
      transition: all 0.3s ease-in-out;
      overflow: hidden;
      border: 1px solid #eaeaea;
      box-sizing: border-box;
      padding: 16px 20px;
      margin-bottom: 0;
      animation: fadeInUp 0.5s ease forwards;
      animation-delay: calc(var(--card-index, 0) * 0.1s);
      opacity: 0;
      transform-origin: center;

      &:hover {
        box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
      }

      &:active {
        transform: scale(0.98);
      }

      .plugin-card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;

        .plugin-info {
          display: flex;
          align-items: center;

          .plugin-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            margin-right: 12px;
            object-fit: cover;
          }

          .el-icon-connection {
            font-size: 24px;
            color: #409eff;
            margin-right: 12px;
          }

          .plugin-title {
            h3 {
              margin: 0;
              font-size: 16px;
              line-height: 1.5;
              color: #1f2937;
            }

            .plugin-type {
              margin: 4px 0 0;
              font-size: 13px;
              color: #6b7280;
            }
          }
        }
      }

      .plugin-desc {
        margin: 0 0 16px;
        font-size: 14px;
        color: #4b5563;
        line-height: 1.5;
        height: 42px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .plugin-footer {
        position: absolute;
        bottom: 16px;
        left: 20px;
        right: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &.builtin-plugin {
          justify-content: flex-end;

          >span {
            margin-right: 4px;

            &:last-child {
              margin-right: 0;
            }
          }
        }

        .plugin-url {
          font-size: 13px;
          color: #6b7280;
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .create-card {
      position: relative;
      width: 100%;
      height: 160px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: #fff;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease-in-out;
      border: 1px solid #eaeaea;
      box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.05);
      box-sizing: border-box;
      animation: fadeInUp 0.5s ease forwards;
      animation-delay: 0.3s;
      opacity: 0;

      &:hover {
        border-color: var(--el-color-primary);
        box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
        background: var(--el-color-primary-light-9);
        transform: translateY(-5px);

        i,
        span {
          color: var(--el-color-primary);
        }

        i {
          transform: rotate(90deg);
        }
      }

      &:active {
        transform: scale(0.98);
      }

      i {
        font-size: 32px;
        margin-bottom: 12px;
        color: #9ca3af;
        transition: all 0.3s ease;
      }

      span {
        font-size: 16px;
        font-weight: 500;
        color: #9ca3af;
        transition: color 0.3s ease;
      }
    }
  }
}

.loading-container {
  width: 100%;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .loading-text {
    color: #909399;
    margin-top: 120px;
    font-size: 14px;
  }
}

.form-item {
  margin-bottom: 24px;

  :deep(.el-form-item__label) {
    padding-bottom: 8px;
    line-height: 1;
    color: #606266;
  }
}

.full-width {
  width: 100%;
}

:deep(.el-dialog) {
  border-radius: 8px;

  .el-dialog__header {
    padding: 20px 20px 10px;
    margin-right: 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .el-dialog__body {
    padding: 24px 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: 1px solid #f0f0f0;
  }
}

// 插件配置弹窗样式
.plugin-config-content {
  .plugin-info-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    .plugin-icon-wrapper {
      margin-right: 16px;

      .plugin-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        object-fit: cover;
        border: 1px solid #eaeaea;
      }

      .plugin-icon-default {
        width: 48px;
        height: 48px;
        font-size: 28px;
        color: #409eff;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f7fa;
        border-radius: 8px;
        border: 1px solid #eaeaea;
      }
    }

    .plugin-name {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
      line-height: 1.4;
    }
  }

  .plugin-details {
    .detail-item {
      margin-bottom: 16px;
      display: flex;
      align-items: flex-start;

      &:last-child {
        margin-bottom: 0;
      }

      .detail-label {
        min-width: 80px;
        font-size: 14px;
        color: #606266;
        font-weight: 500;
        line-height: 1.5;
      }

      .detail-value {
        flex: 1;
        font-size: 14px;
        color: #303133;
        line-height: 1.5;
        word-break: break-all;
      }
    }
  }
}

// 联系客服弹窗样式
.contact-content {
  .contact-item {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 12px;
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
    }

    .phone-number {
      margin: 0;
      font-size: 18px;
      color: #409eff;
      font-weight: 600;
      letter-spacing: 1px;
    }

    .qr-code {
      text-align: center;

      img {
        width: 120px;
        height: 120px;
        object-fit: cover;
        border-radius: 8px;
        border: 1px solid #eaeaea;
      }

      .qr-tip {
        margin: 8px 0 0;
        font-size: 12px;
        color: #909399;
      }
    }
  }
}
</style>
