<template>
  <div class="page flex-col">
    <router-view
      v-if="$route.params.id || $route.path.includes('/new')"
    ></router-view>
    <div class="block_1 flex-row" v-else>
      <div class="block_3 flex-col">
        <!-- 页面头部 -->
        <div class="block_4 flex-row">
          <span class="text_12">插件</span>
          <div class="text_13">查看帮助文档</div>
          <div class="section_17 flex-row" @click="handleCreatePlugin">
            <i class="el-icon-plus thumbnail_22"></i>
            <span class="text_14">新建插件</span>
          </div>
        </div>

        <!-- 自定义插件区域 -->
        <span class="text_15">自定义插件</span>

        <!-- 搜索框 -->
        <div class="block_5 flex-row">
          <i class="el-icon-search thumbnail_23"></i>
          <span class="text_16">搜索插件</span>
        </div>

        <!-- 自定义插件卡片容器 - 响应式4列布局 -->
        <div
          class="custom-plugins-grid"
          v-loading="customLoading"
          element-loading-text="加载自定义插件中..."
        >
          <!-- 动态渲染插件卡片 -->
          <div
            v-for="(plugin, index) in pluginList.slice(0, 4)"
            :key="plugin.id"
            class="custom-plugin-card flex-col"
            :style="{ '--card-index': index }"
            @click="handleEditPlugin(plugin)"
          >
            <div class="section_18 flex-row">
              <img
                v-if="plugin.headSculpture"
                :src="plugin.headSculpture"
                class="label_3"
              />
              <div v-else class="label_3 default-icon">
                <i class="el-icon-connection"></i>
              </div>
              <div class="text-wrapper_2 flex-col justify-between">
                <span class="text_17">{{ plugin.name }}</span>
                <span class="text_18">{{ plugin.description }}</span>
              </div>
              <div class="text-wrapper_3 flex-col">
                <span class="text_19">{{ index % 2 === 0 ? '维护中' : '已启用' }}</span>
              </div>
            </div>
            <div class="text-wrapper_4 flex-col">
              <span class="text_20">{{ plugin.type || 'http' }}</span>
            </div>
            <div class="image_8"></div>
            <div class="section_19 flex-row">
              <span class="text_21">最近更新 {{ formatDate(plugin.updatedAt) }}</span>
              <div class="text-wrapper_5 flex-col">
                <span class="text_22">编辑</span>
              </div>
              <div class="label_4 action-icon">
                <i class="el-icon-more"></i>
              </div>
            </div>
          </div>

          <!-- 空状态占位卡片 -->
          <div
            v-for="index in Math.max(0, 4 - pluginList.length)"
            :key="`empty-${index}`"
            class="custom-plugin-card empty-card flex-col"
            @click="handleCreatePlugin"
          >
            <div class="empty-card-content">
              <i class="el-icon-plus empty-card-icon"></i>
              <span class="empty-card-text">创建新插件</span>
            </div>
          </div>
        </div>

        <!-- 内置插件区域 -->
        <div class="block_16 flex-row">
          <span class="text_41">内置插件</span>
          <div class="image-text_12 flex-row" @click="showContactDialog = true">
            <i class="el-icon-service thumbnail_25"></i>
            <span class="text-group_12">联系客服上架插件</span>
          </div>
        </div>

        <!-- 搜索框 -->
        <div class="block_17 flex-row">
          <div class="image-text_13 flex-row justify-between">
            <i class="el-icon-search thumbnail_26"></i>
            <span class="text-group_13">搜索插件</span>
          </div>
        </div>

        <!-- 内置插件分类选择 -->
        <div class="block_18 flex-row ">
          <div
            class="box_2 flex-row"
            :class="{ active: activeBuiltinTab === 'all' }"
            @click="activeBuiltinTab = 'all'; handleBuiltinTabChange()"
          >
            <div class="image-text_14 flex-row justify-between">
              <i class="el-icon-menu thumbnail_27"></i>
              <span class="text-group_14">全部</span>
            </div>
          </div>
          <div
            class="box_3 flex-row"
            :class="{ active: activeBuiltinTab === 'basic' }"
            @click="activeBuiltinTab = 'basic'; handleBuiltinTabChange()"
          >
            <div class="image-text_15 flex-row justify-between">
              <i class="el-icon-setting thumbnail_28"></i>
              <span class="text-group_15">基础工具</span>
            </div>
          </div>
          <div
            class="box_4 flex-row"
            :class="{ active: activeBuiltinTab === 'search' }"
            @click="activeBuiltinTab = 'search'; handleBuiltinTabChange()"
          >
            <div class="image-text_16 flex-row justify-between">
              <i class="el-icon-search thumbnail_29"></i>
              <span class="text-group_16">搜索工具</span>
            </div>
          </div>
          <div
            class="box_5 flex-row"
            :class="{ active: activeBuiltinTab === 'content' }"
            @click="activeBuiltinTab = 'content'; handleBuiltinTabChange()"
          >
            <div class="image-text_17 flex-row justify-between">
              <i class="el-icon-document thumbnail_30"></i>
              <span class="text-group_17">内容工具</span>
            </div>
          </div>
          <div
            class="box_6 flex-row"
            :class="{ active: activeBuiltinTab === 'media' }"
            @click="activeBuiltinTab = 'media'; handleBuiltinTabChange()"
          >
            <div class="image-text_18 flex-row justify-between">
              <i class="el-icon-video-camera thumbnail_31"></i>
              <span class="text-group_18">音视频工具</span>
            </div>
          </div>
          <div
            class="box_7 flex-row"
            :class="{ active: activeBuiltinTab === 'image' }"
            @click="activeBuiltinTab = 'image'; handleBuiltinTabChange()"
          >
            <div class="image-text_19 flex-row justify-between">
              <i class="el-icon-picture thumbnail_32"></i>
              <span class="text-group_19">图像工具</span>
            </div>
          </div>
        </div>

        <!-- 内置插件卡片容器 -->
        <div
          class="builtin-plugins-container"
          v-loading="builtinLoading"
          element-loading-text="加载内置插件中..."
        >
          <div class="plugins-grid">
            <!-- 内置插件卡片列表 -->
            <div
              v-for="(plugin, index) in builtinPluginList"
              :key="plugin.id"
              class="builtin-plugin-card flex-col"
              :style="{ '--card-index': index }"
              @click="handleViewBuiltinPlugin(plugin)"
            >
            <div class="box_9 flex-col">
              <div class="group_8 flex-row justify-between">
                <img
                  v-if="plugin.iconUrl || plugin.headSculpture"
                  :src="plugin.iconUrl || plugin.headSculpture"
                  class="label_11"
                />
                <div v-else class="label_11 default-icon">
                  <i class="el-icon-connection"></i>
                </div>
                <div class="group_9 flex-col justify-between">
                  <div class="group_10 flex-row">
                    <span class="text_43">{{ plugin.name }}</span>
                    <div class="group_11 flex-col">
                      <div class="text-wrapper_16">
                        <span class="text_44">{{ plugin.type || 'http' }}</span>
                      </div>
                    </div>
                  </div>
                  <span class="text_46">{{ plugin.shortDesc || plugin.description }}</span>
                </div>
              </div>
              <div class="group_12 flex-row">
                <div class="image-text_20 flex-row justify-between">
                  <i class="el-icon-user thumbnail_33"></i>
                  <span class="text-group_20">{{ plugin.nickName || plugin.author || '官方' }}</span>
                </div>
              </div>
            </div>
          </div>

            <!-- 空状态 -->
            <div
              v-if="!builtinLoading && builtinPluginList.length === 0"
              class="empty-content"
            >
              <i class="el-icon-box empty-icon"></i>
              <p class="empty-text">暂无{{ activeBuiltinTab === 'all' ? '内置' : getBuiltinTabLabel() }}插件</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 插件配置弹窗 -->
    <el-dialog
      title="插件配置"
      :visible.sync="showPluginConfigDialog"
      width="12.8rem"
      :before-close="handleClosePluginConfigDialog"
    >
      <div class="plugin-config-content" v-if="selectedPlugin">
        <div class="plugin-info-header">
          <div class="plugin-icon-wrapper">
            <img
              v-if="selectedPlugin.iconUrl || selectedPlugin.headSculpture"
              :src="selectedPlugin.iconUrl || selectedPlugin.headSculpture"
              class="plugin-icon"
              alt="插件图标"
            />
            <i v-else class="el-icon-connection plugin-icon-default"></i>
          </div>
          <h3 class="plugin-name">{{ selectedPlugin.name }}</h3>
        </div>

        <div class="plugin-details">
          <div class="detail-item">
            <span class="detail-label">插件描述：</span>
            <span class="detail-value">{{
              selectedPlugin.shortDesc || "暂无描述"
            }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">插件说明：</span>
            <span class="detail-value">{{
              selectedPlugin.description || "暂无说明"
            }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">作者：</span>
            <span class="detail-value">{{
              selectedPlugin.nickName || "未知作者"
            }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">价格：</span>
            <span class="detail-value">{{
              selectedPlugin.pluginPoint || "暂无定价"
            }}</span>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleConfirmPluginConfig"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 联系客服弹窗 -->
    <el-dialog
      title="联系客服"
      :visible.sync="showContactDialog"
      width="10.67rem"
      :before-close="handleCloseDialog"
    >
      <div class="contact-content">
        <div class="contact-item">
          <h4>客服电话</h4>
          <p class="phone-number">18848457100</p>
        </div>
        <div class="contact-item">
          <h4>客服企微二维码</h4>
          <div class="qr-code">
            <img src="/images/largemodel/customer.png" alt="客服企微二维码" />
            <p class="qr-tip">扫码添加客服微信</p>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showContactDialog = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { api } from "@/api/request"

export default {
  name: "CreatePluginPage",
  data() {
    return {
      pluginList: [], // 自定义插件列表
      builtinPluginList: [], // 内置插件列表（经过过滤的）
      allBuiltinPlugins: [], // 所有内置插件数据
      allPluginsData: [], // 存储所有从/api/v1/Plugin/list获取的数据
      customLoading: false, // 自定义插件加载状态
      builtinLoading: false, // 内置插件加载状态
      showContactDialog: false,
      showPluginConfigDialog: false, // 插件配置弹窗显示状态
      selectedPlugin: null, // 当前选中的插件
      activeBuiltinTab: "all",
    };
  },
  created() {
    this.fetchAllPluginData();
  },
  methods: {
    // 获取所有插件数据
    async fetchAllPluginData() {
      // 并行加载两个接口的数据
      await Promise.all([this.fetchCustomPluginList(), this.fetchPluginList()]);
    },

    // 获取自定义插件列表（保持原有接口不变）
    async fetchCustomPluginList() {
      try {
        this.customLoading = true;
        const params = {
          skipCount: 0,
          maxResultCount: 100,
          category: 2, // 自定义插件
        };
        const res = await api.apiPlugin.getList(params);
        if (res.isSuccess) {
          this.pluginList = res.data.items.map((item) => ({
            id: item.id,
            name: item.name,
            type: item.httpTypeCode,
            description: item.description,
            headSculpture: item.headSculpture,
            url: item.url,
            headers: item.headers,
            parameters: item.parameters,
            responseFields: item.responseFields,
          }));
        }
      } catch (error) {
        console.error("获取自定义插件列表失败:", error);
      } finally {
        this.customLoading = false;
      }
    },

    // 获取插件列表（新接口）
    async fetchPluginList() {
      try {
        this.builtinLoading = true;
        const params = {
          skipCount: 0,
          maxResultCount: 1000, // 获取更多数据用于前端过滤
        };
        const res = await api.plugin.getList(params);
        if (res.isSuccess) {
          this.allPluginsData = res.data.items || [];

          // 处理数据分类
          this.processPluginData();
        }
      } catch (error) {
        console.error("获取插件列表失败:", error);
      } finally {
        this.builtinLoading = false;
      }
    },

    // 处理插件数据分类
    processPluginData() {
      const customPlugins = [];
      const builtinPlugins = [];

      this.allPluginsData.forEach((item) => {
        const plugin = {
          id: item.id,
          name: item.name,
          type: item.httpTypeCode || item.type || "HTTP",
          description: item.description,
          shortDesc: item.shortDesc, // 保留短描述
          headSculpture: item.headSculpture,
          iconUrl: item.iconUrl, // 保留图标URL
          nickName: item.nickName, // 保留昵称
          author: item.author, // 保留作者
          pluginPoint: item.pluginPoint, // 保留价格
          url: item.url,
          headers: item.headers,
          parameters: item.parameters,
          responseFields: item.responseFields,
          tags: item.tags || [], // 改为tags数组
          category: item.category,
        };

        // tags包含1且category为2的数据放到自定义插件数组中
        if ((item.tags || []).includes(1) && item.category === 2) {
          customPlugins.push(plugin);
        } else {
          // 其他的放在内置插件数组中
          builtinPlugins.push(plugin);
        }
      });

      // 将新接口的自定义插件合并到现有的自定义插件列表中
      this.pluginList = [...this.pluginList, ...customPlugins];

      // 设置内置插件列表，并应用当前分类过滤
      this.allBuiltinPlugins = builtinPlugins;
      this.filterBuiltinPlugins();
    },

    // 过滤内置插件（前端过滤）
    filterBuiltinPlugins() {
      if (this.activeBuiltinTab === "all") {
        this.builtinPluginList = this.allBuiltinPlugins || [];
      } else {
        const targetTag = this.getTagByTab(this.activeBuiltinTab);
        this.builtinPluginList = (this.allBuiltinPlugins || []).filter(
          (plugin) => (plugin.tags || []).includes(targetTag)
        );
      }
    },

    // 根据tab获取对应的tag值（这里需要根据图片中的枚举来配置）
    getTagByTab(tab) {
      const tagMap = {
        basic: 2, // 基础工具
        search: 3, // 搜索工具
        content: 4, // 内容工具
        image: 5, // 图像工具
        media: 6, // 音视频工具
        other: 99, // 其他
      };
      return tagMap[tab] || null;
    },
    formatDate(dateString) {
      if (!dateString) return '未知时间'
      const date = new Date(dateString)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },

    handleCreatePlugin() {
      this.$router.push("/create/plugin/new");
    },
    handleEditPlugin(plugin) {
      this.$router.push(`/create/plugin/edit/${plugin.id}`);
      console.log(plugin);
    },
    handleCloseDialog() {
      this.showContactDialog = false;
    },
    // 切换内置插件分类时进行前端过滤
    handleBuiltinTabChange() {
      this.filterBuiltinPlugins();
    },

    // 查看内置插件详情
    handleViewBuiltinPlugin(plugin) {
      // 如果插件是MCPl插件，跳转到编辑页面（查看模式）
      if (plugin.tags.includes(1)) {
        this.$router.push(`/create/plugin/view/${plugin.id}`);
      } else {
        // 其他类型的内置插件显示配置弹窗
        this.selectedPlugin = plugin;
        this.showPluginConfigDialog = true;
      }
    },

    // 关闭插件配置弹窗
    handleClosePluginConfigDialog() {
      this.showPluginConfigDialog = false;
      this.selectedPlugin = null;
    },

    // 确定插件配置
    handleConfirmPluginConfig() {
      this.showPluginConfigDialog = false;
      this.selectedPlugin = null;
      // 这里可以添加后续的插件配置逻辑
    },

    getBuiltinTabLabel() {
      const tabLabels = {
        all: "全部",
        basic: "基础工具",
        search: "搜索工具",
        content: "内容工具",
        image: "图像工具",
        media: "音视频工具",
        other: "其他",
      };
      return tabLabels[this.activeBuiltinTab] || "全部";
    },

    // 获取插件类型对应的中文标签
    getPluginTypeLabel(type) {
      const typeLabels = {
        1: "MCP插件", // 额外增加的MCP
        2: "基础工具", // 基础工具 = 2
        3: "搜索工具", // 搜索工具 = 3
        4: "内容工具", // 内容工具 = 4
        5: "图像工具", // 图像工具 = 5
        6: "音视频工具", // 音视频工具 = 6
        99: "其他", // 其他 = 99
      };
      return typeLabels[type] || typeLabels[parseInt(type)] || "其他插件";
    },

    // 获取插件分类对应的中文标签（基于tag字段）
    getPluginCategoryLabel(tag) {
      const categoryLabels = {
        1: "MCP插件", // 额外增加的MCP
        2: "基础工具", // 基础工具 = 2
        3: "搜索工具", // 搜索工具 = 3
        4: "内容工具", // 内容工具 = 4
        5: "图像工具", // 图像工具 = 5
        6: "音视频工具", // 音视频工具 = 6
        99: "其他", // 其他 = 99
      };
      return categoryLabels[tag] || categoryLabels[parseInt(tag)] || "其他插件";
    },
  },
};
</script>

<style scoped>
/* 基础布局样式 */
.page {
  background-color: rgba(242, 246, 252, 1);
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.block_1 {
  width: 100%;
  height: 100%;
}

.block_3 {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 0.32rem; /* 12px转换为rem */
  width: calc(100% - 2.67rem); /* 100px转换为rem */
  height: calc(100vh - 1.33rem); /* 50px转换为rem */
  border: 0.013rem solid rgba(235, 236, 241, 1); /* 0.5px转换为rem */
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

/* 页面头部样式 */
.block_4 {
  width: calc(100% - 2.13rem); /* 80px转换为rem */
  height: 0.96rem; /* 36px转换为rem */
  margin: 0.85rem 0 0 1.07rem; /* 32px 0 0 40px转换为rem */
  display: flex;
  align-items: center;
  position: relative;
}

.text_12 {
  width: 1.28rem; /* 48px转换为rem */
  height: 0.88rem; /* 33px转换为rem */
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.64rem; /* 24px转换为rem */
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem; /* 33px转换为rem */
}

.text_13 {
  width: 2.24rem; /* 84px转换为rem */
  height: 0.53rem; /* 20px转换为rem */
  overflow-wrap: break-word;
  color: rgba(37, 109, 255, 1);
  font-size: 0.37rem; /* 14px转换为rem */
  text-align: center;
  white-space: nowrap;
  line-height: 0.53rem; /* 20px转换为rem */
  margin: 0.19rem 0 0 34.45rem; /* 7px 0 0 1294px转换为rem */
  cursor: pointer;
}

.section_17 {
  background-color: rgba(37, 109, 255, 1);
  border-radius: 0.21rem; /* 8px转换为rem */
  width: 2.72rem; /* 102px转换为rem */
  height: 0.96rem; /* 36px转换为rem */
  margin-left: 0.64rem; /* 24px转换为rem */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.section_17:hover {
  background-color: rgba(30, 92, 230, 1);
}

.thumbnail_22 {
  width: 0.27rem; /* 10px转换为rem */
  height: 0.27rem; /* 10px转换为rem */
  color: rgba(255, 255, 255, 1);
  font-size: 0.27rem;
}

.text_14 {
  width: 1.49rem; /* 56px转换为rem */
  height: 0.53rem; /* 20px转换为rem */
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.37rem; /* 14px转换为rem */
  text-align: left;
  white-space: nowrap;
  line-height: 0.53rem; /* 20px转换为rem */
}

/* 自定义插件标题 */
.text_15 {
  width: 2.13rem; /* 80px转换为rem */
  height: 0.59rem; /* 22px转换为rem */
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.43rem; /* 16px转换为rem */
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.59rem; /* 22px转换为rem */
  margin: 0.77rem 0 0 1.07rem; /* 29px 0 0 40px转换为rem */
}

/* 搜索框样式 */
.block_5 {
  background-color: rgba(244, 246, 248, 1);
  border-radius: 0.21rem; /* 8px转换为rem */
  width: 6.4rem; /* 240px转换为rem */
  height: 0.96rem; /* 36px转换为rem */
  margin: 0.64rem 0 0 1.07rem; /* 24px 0 0 40px转换为rem */
  display: flex;
  align-items: center;
  padding: 0 0.35rem; /* 13px转换为rem */
}

.thumbnail_23 {
  width: 0.4rem; /* 15px转换为rem */
  height: 0.43rem; /* 16px转换为rem */
  margin: 0.29rem 0 0 0.35rem; /* 11px 0 0 13px转换为rem */
  color: rgba(186, 186, 186, 1);
  font-size: 0.4rem;
}

.text_16 {
  width: 1.49rem; /* 56px转换为rem */
  height: 0.53rem; /* 20px转换为rem */
  overflow-wrap: break-word;
  color: rgba(186, 186, 186, 1);
  font-size: 0.37rem; /* 14px转换为rem */
  text-align: left;
  white-space: nowrap;
  line-height: 0.53rem; /* 20px转换为rem */
  margin: 0.21rem 3.84rem 0 0.32rem; /* 8px 144px 0 12px转换为rem */
}

/* 自定义插件卡片容器 - 考虑左侧菜单栏的精确计算 */
.custom-plugins-grid {
  display: flex;
  flex-wrap: wrap;
  /* 主内容区域宽度 = 100vw - 246px(菜单栏) - 12px(右边距) - 40px(左边距) - 32px(右内边距) */
  width: calc(100vw - 246px - 12px - 40px - 32px);
  margin: 0.85rem 0 0 1.07rem; /* 32px 0 0 40px转换为rem */
  min-height: 4.29rem; /* 161px转换为rem */
  box-sizing: border-box;
  gap: 0.53rem; /* 20px转换为rem */
}

/* 默认4列布局 - 精确计算每个卡片宽度 */
.custom-plugin-card {
  width: calc((100% - 1.59rem) / 4); /* (总宽度 - 3个间距) / 4个卡片 */
  flex-shrink: 0;
}

/* 响应式断点 - 根据屏幕宽度调整列数和卡片宽度 */
/* 超大屏幕：4列布局 */
@media (min-width: 1600px) {
  .custom-plugins-grid {
    gap: 0.53rem; /* 20px */
  }
  .custom-plugin-card {
    width: calc((100% - 1.59rem) / 4); /* (总宽度 - 3个间距) / 4个卡片 */
  }
}

/* 大屏幕：4列布局，减小间距 */
@media (max-width: 1599px) and (min-width: 1200px) {
  .custom-plugins-grid {
    gap: 0.4rem; /* 15px */
  }
  .custom-plugin-card {
    width: calc((100% - 1.2rem) / 4); /* (总宽度 - 3个间距) / 4个卡片 */
  }
}

/* 中屏幕：3列布局 */
@media (max-width: 1199px) and (min-width: 900px) {
  .custom-plugins-grid {
    gap: 0.4rem; /* 15px */
  }
  .custom-plugin-card {
    width: calc((100% - 0.8rem) / 3); /* (总宽度 - 2个间距) / 3个卡片 */
  }
}

/* 小屏幕：2列布局 */
@media (max-width: 899px) and (min-width: 600px) {
  .custom-plugins-grid {
    gap: 0.32rem; /* 12px */
  }
  .custom-plugin-card {
    width: calc((100% - 0.32rem) / 2); /* (总宽度 - 1个间距) / 2个卡片 */
  }
}

/* 超小屏幕：1列布局 */
@media (max-width: 599px) {
  .custom-plugins-grid {
    gap: 0.27rem; /* 10px */
  }
  .custom-plugin-card {
    width: 100%; /* 单列占满宽度 */
  }
}

/* 自定义插件卡片样式 - 基础样式 */
.custom-plugin-card {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 0.21rem; /* 8px转换为rem */
  height: 4.29rem; /* 161px转换为rem */
  border: 0.013rem solid rgba(235, 236, 241, 1); /* 0.5px转换为rem */
  cursor: pointer;
  transition: all 0.3s ease;
  animation: fadeInUp 0.5s ease forwards;
  animation-delay: calc(var(--card-index, 0) * 0.1s);
  opacity: 0;
  transform: translateY(0.53rem); /* 20px转换为rem */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  /* 宽度在响应式规则中设置 */
}

/* 自定义插件卡片hover效果 */
.custom-plugin-card:hover {
  box-shadow: 0 0.11rem 0.53rem rgba(0, 0, 0, 0.1); /* 0 4px 20px转换为rem */
  transform: translateY(-0.05rem); /* -2px转换为rem */
}

/* 空状态卡片样式 */
.empty-card {
  border: 0.053rem dashed rgba(186, 186, 186, 0.5); /* 2px转换为rem */
  background-color: rgba(250, 250, 250, 1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-card:hover {
  border-color: rgba(37, 109, 255, 0.5);
  background-color: rgba(245, 247, 250, 1);
}

.empty-card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.27rem; /* 10px转换为rem */
}

.empty-card-icon {
  font-size: 1.07rem; /* 40px转换为rem */
  color: rgba(186, 186, 186, 1);
}

.empty-card:hover .empty-card-icon {
  color: rgba(37, 109, 255, 1);
}

.empty-card-text {
  font-size: 0.37rem; /* 14px转换为rem */
  color: rgba(136, 136, 136, 1);
}

.empty-card:hover .empty-card-text {
  color: rgba(37, 109, 255, 1);
}

/* 动画关键帧 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(0.53rem); /* 20px转换为rem */
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 自定义插件卡片内部元素样式 - 响应式调整 */
.section_18 {
  width: calc(100% - 0.85rem); /* 减去左右边距 */
  height: 1.33rem; /* 50px转换为rem */
  margin: 0.32rem 0 0 0.43rem; /* 12px 0 0 16px转换为rem */
  display: flex;
  align-items: flex-start;
}

.label_3 {
  width: 1.17rem; /* 44px转换为rem */
  height: 1.17rem; /* 44px转换为rem */
  margin-top: 0.11rem; /* 4px转换为rem */
  border-radius: 50%; /* 圆形头像 */
  object-fit: cover;
}

.text-wrapper_2 {
  flex: 1; /* 自适应宽度 */
  height: 1.23rem; /* 46px转换为rem */
  margin: 0.11rem 0 0 0.32rem; /* 4px 0 0 12px转换为rem */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.text_17 {
  height: 0.59rem; /* 22px转换为rem */
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.43rem; /* 16px转换为rem */
  text-transform: uppercase;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 0.59rem; /* 22px转换为rem */
  overflow: hidden;
  text-overflow: ellipsis;
}

.text_18 {
  height: 0.53rem; /* 20px转换为rem */
  overflow-wrap: break-word;
  color: rgba(136, 136, 136, 1);
  font-size: 0.32rem; /* 12px转换为rem */
  text-align: left;
  white-space: nowrap;
  line-height: 0.53rem; /* 20px转换为rem */
  margin-top: 0.11rem; /* 4px转换为rem */
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-wrapper_3 {
  background-color: rgba(244, 246, 248, 1);
  border-radius: 0.11rem; /* 4px转换为rem */
  height: 0.53rem; /* 20px转换为rem */
  margin-left: 0.11rem; /* 4px转换为rem */
  width: 1.39rem; /* 52px转换为rem */
  display: flex;
  align-items: center;
  justify-content: center;
}

.text_19 {
  width: 0.96rem; /* 36px转换为rem */
  height: 0.48rem; /* 18px转换为rem */
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 0.32rem; /* 12px转换为rem */
  text-align: left;
  white-space: nowrap;
  line-height: 0.48rem; /* 18px转换为rem */

}

.text-wrapper_4 {
  height: 0.53rem; /* 20px转换为rem */
  background-color: rgba(62, 199, 70, 0.1);
  border-radius: 0.11rem; /* 4px转换为rem */
  width: 1.01rem; /* 38px转换为rem */
  margin: 0.21rem 0 0 1.92rem; /* 8px 0 0 72px转换为rem */
  display: flex;
  align-items: center;
  justify-content: center;
}

.text_20 {
  width: 0.75rem; /* 28px转换为rem */
  height: 0.48rem; /* 18px转换为rem */
  overflow-wrap: break-word;
  color: rgba(62, 199, 70, 1);
  font-size: 0.32rem; /* 12px转换为rem */
  text-transform: uppercase;
  text-align: left;
  white-space: nowrap;
  line-height: 0.48rem; /* 18px转换为rem */
  margin: 0.03rem 0 0 0.13rem; /* 1px 0 0 5px转换为rem */
}

.image_8 {
  width: calc(100% - 0.85rem); /* 减去左右边距 */
  height: 0.027rem; /* 1px转换为rem */
  margin: 0.43rem 0 0 0.43rem; /* 16px 0 0 16px转换为rem */
  background-color: rgba(235, 236, 241, 1);
}

.section_19 {
  width: calc(100% - 0.8rem); /* 减去左右边距 */
  height: 0.8rem; /* 30px转换为rem */
  margin: 0.32rem 0 0.32rem 0.4rem; /* 12px 0 12px 15px转换为rem */
  display: flex;
  align-items: center;
}

.text_21 {
  width: 4.61rem; /* 173px转换为rem */
  height: 0.53rem; /* 20px转换为rem */
  overflow-wrap: break-word;
  color: rgba(186, 186, 186, 1);
  font-size: 0.32rem; /* 12px转换为rem */
  text-align: left;
  white-space: nowrap;
  line-height: 0.53rem; /* 20px转换为rem */
  margin-top: 0.13rem; /* 5px转换为rem */
}

.text-wrapper_5 {
  background-color: rgba(244, 246, 248, 1);
  border-radius: 0.11rem; /* 4px转换为rem */
  height: 0.8rem; /* 30px转换为rem */
  margin-left: 1.92rem; /* 72px转换为rem */
  width: 1.39rem; /* 52px转换为rem */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.text-wrapper_5:hover {
  background-color: #e1f3d8;
}

.text_22 {
  width: 0.75rem; /* 28px转换为rem */
  height: 0.53rem; /* 20px转换为rem */
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.37rem; /* 14px转换为rem */
  text-align: left;
  white-space: nowrap;
  line-height: 0.53rem; /* 20px转换为rem */
  margin: 0.13rem 0 0 0.32rem; /* 5px 0 0 12px转换为rem */
}

.label_4 {
  width: 1.17rem; /* 44px转换为rem */
  height: 0.8rem; /* 30px转换为rem */
  margin-left: 0.21rem; /* 8px转换为rem */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}







/* 默认图标样式 */
.default-icon {
  background-color: rgba(244, 246, 248, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.default-icon i {
  font-size: 0.53rem; /* 20px转换为rem */
  color: rgba(37, 109, 255, 1);
}

/* 动作图标样式 */
.action-icon i {
  font-size: 0.43rem; /* 16px转换为rem */
  color: rgba(153, 153, 153, 1);
}

.plugin-card:hover {
  box-shadow: 0 0.11rem 0.32rem rgba(0, 0, 0, 0.1); /* 4px 12px转换为rem */
}

.section_18 {
  width: 9.39rem; /* 352px转换为rem */
  height: 1.33rem; /* 50px转换为rem */
  margin: 0.32rem 0 0 0.43rem; /* 12px 0 0 16px转换为rem */
  display: flex;
  align-items: flex-start;
}

.label_3 {
  width: 1.17rem; /* 44px转换为rem */
  height: 1.17rem; /* 44px转换为rem */
  margin-top: 0.11rem; /* 4px转换为rem */
  border-radius: 0.21rem; /* 8px转换为rem */
  object-fit: cover;
}

.default-icon {
  background-color: rgba(244, 246, 248, 1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.default-icon i {
  font-size: 0.53rem; /* 20px转换为rem */
  color: rgba(37, 109, 255, 1);
}

.text-wrapper_2 {
  width: 6.4rem; /* 240px转换为rem */
  height: 1.23rem; /* 46px转换为rem */
  margin: 0.11rem 0 0 0.32rem; /* 4px 0 0 12px转换为rem */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.text_17 {
  width: 2.75rem; /* 103px转换为rem */
  height: 0.59rem; /* 22px转换为rem */
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.43rem; /* 16px转换为rem */
  text-transform: uppercase;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 0.59rem; /* 22px转换为rem */
  overflow: hidden;
  text-overflow: ellipsis;
}

.text_18 {
  width: 6.4rem; /* 240px转换为rem */
  height: 0.53rem; /* 20px转换为rem */
  overflow-wrap: break-word;
  color: rgba(136, 136, 136, 1);
  font-size: 0.32rem; /* 12px转换为rem */
  text-align: left;
  white-space: nowrap;
  line-height: 0.53rem; /* 20px转换为rem */
  margin-top: 0.11rem; /* 4px转换为rem */
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 内置插件相关样式 */
.block_16 {
  width: calc(100% - 2.13rem); /* 80px转换为rem */
  height: 0.96rem; /* 36px转换为rem */
  margin: 0.85rem 0 0 1.07rem; /* 32px 0 0 40px转换为rem */
  display: flex;
  align-items: center;
  position: relative;
}

.text_41 {
  width: 2.13rem; /* 80px转换为rem */
  height: 0.59rem; /* 22px转换为rem */
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.43rem; /* 16px转换为rem */
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.59rem; /* 22px转换为rem */
}

.image-text_12 {
  background-color: rgba(37, 109, 255, 1);
  border-radius: 0.21rem; /* 8px转换为rem */
  width: 4.27rem; /* 160px转换为rem */
  height: 0.96rem; /* 36px转换为rem */
  margin-left: 34.45rem; /* 1294px转换为rem */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  gap: 0.21rem; /* 8px图标和文字间距 */
}

.image-text_12:hover {
  background-color: rgba(30, 92, 230, 1);
}

.thumbnail_25 {
  width: 0.37rem; /* 14px转换为rem */
  height: 0.37rem; /* 14px转换为rem */
  color: rgba(255, 255, 255, 1);
  font-size: 0.37rem;
  margin: 0; /* 移除margin，使用gap控制间距 */
}

.text-group_12 {
  height: 0.53rem; /* 20px转换为rem */
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.37rem; /* 14px转换为rem */
  text-align: center;
  white-space: nowrap;
  line-height: 0.53rem; /* 20px转换为rem */
  margin: 0; /* 移除margin，使用gap控制间距 */
}

/* 内置插件搜索框 */
.block_17 {
  width: calc(100% - 2.13rem); /* 80px转换为rem */
  height: 0.96rem; /* 36px转换为rem */
  margin: 0.64rem 0 0 1.07rem; /* 24px 0 0 40px转换为rem */
  display: flex;
  align-items: center;
}

.image-text_13 {
  background-color: rgba(244, 246, 248, 1);
  border-radius: 0.21rem; /* 8px转换为rem */
  width: 6.4rem; /* 240px转换为rem */
  height: 0.96rem; /* 36px转换为rem */
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 0.35rem; /* 13px转换为rem */
  gap: 0.21rem; /* 8px图标和文字间距 */
}

.thumbnail_26 {
  width: 0.4rem; /* 15px转换为rem */
  height: 0.43rem; /* 16px转换为rem */
  color: rgba(186, 186, 186, 1);
  font-size: 0.4rem;
  margin: 0; /* 移除margin，使用gap控制间距 */
}

.text-group_13 {
  height: 0.53rem; /* 20px转换为rem */
  overflow-wrap: break-word;
  color: rgba(186, 186, 186, 1);
  font-size: 0.37rem; /* 14px转换为rem */
  text-align: left;
  white-space: nowrap;
  line-height: 0.53rem; /* 20px转换为rem */
  margin: 0; /* 移除margin，使用gap控制间距 */
}

/* 内置插件分类选择 */
.block_18 {
  width: calc(100% - 2.13rem); /* 80px转换为rem */
  margin: 0.64rem 0 0 1.07rem; /* 24px 0 0 40px转换为rem */
  display: flex;
  gap: 0.53rem; /* 20px转换为rem */
  flex-wrap: wrap;
  justify-content: flex-start;
}

.box_2, .box_3, .box_4, .box_5, .box_6, .box_7 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 0.21rem; /* 8px转换为rem */
  height: 0.96rem; /* 36px转换为rem */
  border: 0.027rem solid rgba(220, 223, 230, 1); /* 1px转换为rem */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0 0.43rem; /* 16px转换为rem */
}

.box_2.active, .box_3.active, .box_4.active, .box_5.active, .box_6.active, .box_7.active {
  background-color: rgba(37, 109, 255, 1);
  border-color: rgba(37, 109, 255, 1);
}

.box_2:hover, .box_3:hover, .box_4:hover, .box_5:hover, .box_6:hover, .box_7:hover {
  background-color: rgba(245, 247, 250, 1);
  border-color: rgba(37, 109, 255, 1);
}

.image-text_14, .image-text_15, .image-text_16, .image-text_17, .image-text_18, .image-text_19 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.21rem; /* 8px转换为rem */
  width: 100%;
  height: 100%;
}

.thumbnail_27, .thumbnail_28, .thumbnail_29, .thumbnail_30, .thumbnail_31, .thumbnail_32 {
  width: 0.37rem; /* 14px转换为rem */
  height: 0.37rem; /* 14px转换为rem */
  color: rgba(102, 102, 102, 1);
  font-size: 0.37rem;
}

.active .thumbnail_27, .active .thumbnail_28, .active .thumbnail_29, .active .thumbnail_30, .active .thumbnail_31, .active .thumbnail_32 {
  color: rgba(255, 255, 255, 1);
}

.text-group_14, .text-group_15, .text-group_16, .text-group_17, .text-group_18, .text-group_19 {
  height: 0.53rem; /* 20px转换为rem */
  overflow-wrap: break-word;
  color: rgba(102, 102, 102, 1);
  font-size: 0.37rem; /* 14px转换为rem */
  text-align: center;
  white-space: nowrap;
  line-height: 0.53rem; /* 20px转换为rem */
  margin: 0; /* 确保没有额外的margin */
}

.active .text-group_14, .active .text-group_15, .active .text-group_16, .active .text-group_17, .active .text-group_18, .active .text-group_19 {
  color: rgba(255, 255, 255, 1);
}

/* 内置插件卡片容器 - 考虑左侧菜单栏的精确计算 */
.builtin-plugins-container {
  /* 主内容区域宽度 = 100vw - 246px(菜单栏) - 12px(右边距) - 40px(左边距) - 32px(右内边距) */
  width: calc(100vw - 246px - 12px - 40px - 32px);
  margin: 0.85rem 0 0 1.07rem; /* 32px 0 0 40px转换为rem */
}

/* 内置插件网格布局 - 改用Flexbox以精确控制宽度 */
.plugins-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.53rem; /* 20px转换为rem */
  width: 100%;
}

/* 默认4列布局 - 精确计算每个内置插件卡片宽度 */
.builtin-plugin-card {
  box-shadow: 0 0 0.27rem 0 rgba(190, 190, 190, 0.3); /* 10px转换为rem */
  background-color: rgba(255, 255, 255, 1);
  border-radius: 0.21rem; /* 8px转换为rem */
  width: calc((100% - 1.59rem) / 4); /* (总宽度 - 3个间距) / 4个卡片 */
  height: 3.73rem; /* 140px转换为rem */
  border: 0.013rem solid rgba(223, 225, 232, 1); /* 0.5px转换为rem */
  cursor: pointer;
  transition: all 0.3s ease;
  animation: fadeInUp 0.5s ease forwards;
  animation-delay: calc(var(--card-index, 0) * 0.1s);
  opacity: 0;
  transform: translateY(0.53rem); /* 20px转换为rem */
  justify-content: flex-start;
  padding: 0; /* 确保内部元素控制间距 */
  overflow: hidden; /* 防止内容溢出 */
  flex-shrink: 0;
  box-sizing: border-box;
}

/* 响应式断点 - 根据屏幕宽度调整内置插件列数和卡片宽度 */
/* 超大屏幕：4列布局 */
@media (min-width: 1600px) {
  .plugins-grid {
    gap: 0.53rem; /* 20px */
  }
  .builtin-plugin-card {
    width: calc((100% - 1.59rem) / 4); /* (总宽度 - 3个间距) / 4个卡片 */
  }
}

/* 大屏幕：4列布局，减小间距 */
@media (max-width: 1599px) and (min-width: 1200px) {
  .plugins-grid {
    gap: 0.4rem; /* 15px */
  }
  .builtin-plugin-card {
    width: calc((100% - 1.2rem) / 4); /* (总宽度 - 3个间距) / 4个卡片 */
  }
}

/* 中屏幕：3列布局 */
@media (max-width: 1199px) and (min-width: 900px) {
  .plugins-grid {
    gap: 0.4rem; /* 15px */
  }
  .builtin-plugin-card {
    width: calc((100% - 0.8rem) / 3); /* (总宽度 - 2个间距) / 3个卡片 */
  }
}

/* 小屏幕：2列布局 */
@media (max-width: 899px) and (min-width: 600px) {
  .plugins-grid {
    gap: 0.32rem; /* 12px */
  }
  .builtin-plugin-card {
    width: calc((100% - 0.32rem) / 2); /* (总宽度 - 1个间距) / 2个卡片 */
  }
}

/* 超小屏幕：1列布局 */
@media (max-width: 599px) {
  .plugins-grid {
    gap: 0.27rem; /* 10px */
  }
  .builtin-plugin-card {
    width: 100%; /* 单列占满宽度 */
  }
}

.builtin-plugin-card:hover {
  box-shadow: 0 0.11rem 0.32rem rgba(0, 0, 0, 0.1); /* 4px 12px转换为rem */
}

.box_9 {
  width: 5.65rem; /* 212px转换为rem */
  height: 2.93rem; /* 110px转换为rem */
  margin: 0.43rem 0 0 0.43rem; /* 16px 0 0 16px转换为rem */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.group_8 {
  width: 5.65rem; /* 212px转换为rem */
  height: 1.17rem; /* 44px转换为rem */
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.label_11 {
  width: 1.17rem; /* 44px转换为rem */
  height: 1.17rem; /* 44px转换为rem */
  border-radius: 0.21rem; /* 8px转换为rem */
  object-fit: cover;
}

.group_9 {
  width: 4.27rem; /* 160px转换为rem */
  height: 1.17rem; /* 44px转换为rem */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.group_10 {
  width: 4.27rem; /* 160px转换为rem */
  height: 0.59rem; /* 22px转换为rem */
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text_43 {
  width: 2.75rem; /* 103px转换为rem */
  height: 0.59rem; /* 22px转换为rem */
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.43rem; /* 16px转换为rem */
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 0.59rem; /* 22px转换为rem */
  overflow: hidden;
  text-overflow: ellipsis;
}

.group_11 {
  height: 0.53rem; /* 20px转换为rem */
  width: 1.01rem; /* 38px转换为rem */
  display: flex;
  flex-direction: column;
}

.text-wrapper_16 {
  background-color: rgba(62, 199, 70, 0.1);
  border-radius: 0.11rem; /* 4px转换为rem */
  height: 0.53rem; /* 20px转换为rem */
  width: 1.01rem; /* 38px转换为rem */
  display: flex;
  align-items: center;
  justify-content: center;
}

.text_44 {
  width: 0.75rem; /* 28px转换为rem */
  height: 0.48rem; /* 18px转换为rem */
  overflow-wrap: break-word;
  color: rgba(62, 199, 70, 1);
  font-size: 0.32rem; /* 12px转换为rem */
  text-transform: uppercase;
  text-align: left;
  white-space: nowrap;
  line-height: 0.48rem; /* 18px转换为rem */
  margin: 0.03rem 0 0 0.13rem; /* 1px 0 0 5px转换为rem */
}

.text_46 {
  width: 4.27rem; /* 160px转换为rem */
  height: 0.53rem; /* 20px转换为rem */
  overflow-wrap: break-word;
  color: rgba(136, 136, 136, 1);
  font-size: 0.32rem; /* 12px转换为rem */
  text-align: left;
  white-space: nowrap;
  line-height: 0.53rem; /* 20px转换为rem */
  overflow: hidden;
  text-overflow: ellipsis;
}

.group_12 {
  width: 2.99rem; /* 112px转换为rem */
  height: 0.53rem; /* 20px转换为rem */
  display: flex;
  align-items: center;
}

.image-text_20 {
  width: 2.99rem; /* 112px转换为rem */
  height: 0.53rem; /* 20px转换为rem */
  display: flex;
  align-items: center;
  gap: 0.21rem; /* 8px转换为rem */
}

.thumbnail_33 {
  width: 0.37rem; /* 14px转换为rem */
  height: 0.37rem; /* 14px转换为rem */
  color: rgba(186, 186, 186, 1);
  font-size: 0.37rem;
}

.text-group_20 {
  width: 2.4rem; /* 90px转换为rem */
  height: 0.53rem; /* 20px转换为rem */
  overflow-wrap: break-word;
  color: rgba(186, 186, 186, 1);
  font-size: 0.32rem; /* 12px转换为rem */
  text-align: left;
  white-space: nowrap;
  line-height: 0.53rem; /* 20px转换为rem */
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(0.53rem); /* 20px转换为rem */
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 通用布局类 */
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.align-center {
  align-items: center;
}

/* 空状态样式 */
.empty-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2.13rem 0; /* 80px转换为rem */
  color: #9ca3af;
  font-size: 0.37rem; /* 14px转换为rem */
  min-height: 5.33rem; /* 200px转换为rem */
}

.empty-icon {
  font-size: 1.71rem; /* 64px转换为rem */
  color: #d1d5db;
  margin-bottom: 0.53rem; /* 20px转换为rem */
}

.empty-text {
  margin: 0;
  color: #6b7280;
  font-size: 0.43rem; /* 16px转换为rem */
  line-height: 1.5;
  font-weight: 500;
}
</style>
